import { supabase } from "$lib/server";
import { json } from "@sveltejs/kit";

interface QuestionData {
    topic: string;
    questionType: string;
    intro: string | null;
    passage: string | null;
    passage2: string | null;
    question: string;
    answerChoices: string[];
    correctAnswer: string;
    explanation: string;
    figure: string | null;
}

export const POST = async ({ request }) => {
    const { simulationId } = await request.json();
    const { data: simulationData, error: simulationError } = await supabase
        .from('simulations')
        .select('*')
        .eq('id', simulationId)
        .maybeSingle();

    if (!simulationData) return json({ success: false, message: "Simulation not found" });

    const { data: figureListData, error: figureListError } = await supabase
        .storage
        .from("Simulation Figures")
        .list(String(simulationId));

    if (figureListError) return json({ success: false, message: figureListError.message });
    const figureList = figureListData.map((figure) => figure.name);

    const modules: { id: string, title: string, questions: any[] }[] = [
        {
            id: "RW1",
            title: "R&W - Module 1",
            questions: simulationData.RW1
        },
        {
            id: "RW2",
            title: "R&W - Module 2",
            questions: simulationData.RW2
        },
        {
            id: "M1",
            title: "Math - Module 1",
            questions: simulationData.M1
        },
        {
            id: "M2",
            title: "Math - Module 2",
            questions: simulationData.M2
        }
    ];
    
    await Promise.all(modules.map(async (module) => {
    module.questions = await Promise.all(module.questions.map(async (questionId, questionIndex) => {
            const { data: questionData, error: questionError } = await supabase
                .from('question')
                .select('topic, questionType, intro, passage, passage2, question, answerChoices, correctAnswer, explanation')
                .eq('id', questionId)
                .single();
    
            if (questionError) {
                throw new Error(questionError.message);
            }

            (questionData as QuestionData).figure = null;

            const questionStringId = `${module.id}_${questionIndex+1}`;
            if (figureList.includes(`${questionStringId}.png`)) {
                const { data: imageData, error: imageError } = await supabase
                    .storage
                    .from("Simulation Figures")
                    .download(`${simulationId}/${questionStringId}.png`);
    
                if (imageError) {
                    throw new Error(imageError.message);
                }
                
                const base64 = await blobToBase64(imageData);
                (questionData as QuestionData).figure = `data:image/png;base64,${base64}`;
            }

            if (questionData.questionType === "Student Notes") {
                questionData.passage = questionData.passage.split(" | ");
            } else if (!["Algebra", "Data Analysis", "Geometry"].includes(questionData.topic)) {
                questionData.passage = questionData.passage.replace(/ \| /g, "<br>")
            }

            if (questionData.answerChoices) {
                questionData.answerChoices.forEach((choice, index) => {
                    questionData.answerChoices[index] = choice.replace(/ \| /g, "<br>");
                });
            }

            questionData.explanation = questionData.explanation.replace(/(\n)+/g, "<br><br>").replace(/\*.*?\*/g, (match) => `<i>${match.slice(1, -1)}</i>`);

            return questionData;
        }));
    }));

    return json({ success: true, data: { modules }});
}


async function blobToBase64(blob: Blob): Promise<string> {
    const arrayBuffer = await blob.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString("base64");
}

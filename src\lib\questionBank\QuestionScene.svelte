<script lang="ts">
	import { innerWidth } from 'svelte/reactivity/window';
	import { P1, P2, P3, MarkBarUI, H4 } from "$lib/ui";
	import Annotate from '$lib/annotate/Annotate.svelte';
    import Clock from './Clock.svelte';

    let {
        questionIndex,
        questionData,
        onAnswerSelect,
        reset,
        updateMarkedInDB = () => {},
        showCorrectAnswer = false,
        showExplanation = false,
        isMarked = $bindable(false),
        onBackClick
    } = $props();

    const isMath = $derived(['Algebra', 'Data Analysis', 'Geometry'].includes(questionData.topic));

    const letters = ['A', 'B', 'C', 'D'];
    let selectedAnswer = $state<number | null>(null);
    let isAnswered = $state(false);
    let isCrossedOut = $state(Array(answerChoices.length).fill(false));

    let setMarked = () => {
        isMarked = !isMarked;
        updateMarkedInDB();
    }

    let isEliminateToolActive = $state(false);

    function convertMarkdownItalic(text: string): string {
        return text.replace(/\*(.*?)\*/g, '<i>$1</i>');
    }

    $effect(() => {
        // Reset when either the reset prop changes or the question changes
        if (reset || question) {
            selectedAnswer = null;
            isAnswered = false;
            isCrossedOut = Array(answerChoices.length).fill(false);
        }
    });

    $effect(() => {
        if (showCorrectAnswer) {
            selectedAnswer = correctAnswer;
            isAnswered = true;
        }
    });

    function handleAnswerSelect(index: number) {
        if (isAnswered) return;
        isCrossedOut = Array(answerChoices.length).fill(false);
        selectedAnswer = index;
        isAnswered = true;
        onAnswerSelect?.(index);
    }

    function handleCrossOut(index: number) {
        if (isAnswered) return;
        isCrossedOut = isCrossedOut.map((val, i) => i === index ? !val : val);
    }

    function isCorrect(index: number) {
        return index === correctAnswer;
    }

    function getButtonClass(index: number) {
        let base = 'question-choice';
        if (isCrossedOut[index]) base += ' question-choice-crossed';
        if (!isAnswered) return base;
        if (isCorrect(index)) return base + ' question-choice-correct';
        if (selectedAnswer === index && !isCorrect(index)) return base + ' question-choice-incorrect';
        return base;
    }



    // Annotate
</script>


<div class="question-panel bg-white border-[1.5px] border-black rounded-lg box-shadow h-full">
    <div class="question-panel-header flex justify-between items-center p-4 border-b-[1.5px] border-black">
        <button onclick={onBackClick} aria-label="Back">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 21 21">
                <g fill="none" fill-rule="evenodd" stroke="#000000" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" transform="matrix(-1 0 0 1 18 3)">
                    <path d="m10.595 10.5 2.905-3-2.905-3"/>
                    <path d="m13.5 7.5h-9"/>
                    <path d="m10.5.5-8 .00224609c-1.1043501.00087167-1.9994384.89621131-2 2.00056153v9.99438478c.0005616 1.1043502.8956499 1.9996898 2 2.0005615l8 .0022461"/>
                </g>
            </svg>					
        </button>
        <H4><Clock /></H4>
        <button class="tool-button" aria-label="Annotation tool">
            <svg width="32" height="32" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 7.74002C22.0008 7.60841 21.9756 7.47795 21.9258 7.35611C21.876 7.23427 21.8027 7.12346 21.71 7.03002L17.47 2.79002C17.3766 2.69734 17.2658 2.62401 17.1439 2.57425C17.0221 2.52448 16.8916 2.49926 16.76 2.50002C16.6284 2.49926 16.4979 2.52448 16.3761 2.57425C16.2543 2.62401 16.1435 2.69734 16.05 2.79002L13.22 5.62002L2.29002 16.55C2.19734 16.6435 2.12401 16.7543 2.07425 16.8761C2.02448 16.9979 1.99926 17.1284 2.00002 17.26V21.5C2.00002 21.7652 2.10537 22.0196 2.29291 22.2071C2.48045 22.3947 2.7348 22.5 3.00002 22.5H7.24002C7.37994 22.5076 7.51991 22.4857 7.65084 22.4358C7.78176 22.3858 7.90073 22.3089 8.00002 22.21L18.87 11.28L21.71 8.50002C21.8013 8.4031 21.8757 8.29155 21.93 8.17002C21.9397 8.09031 21.9397 8.00973 21.93 7.93002C21.9347 7.88347 21.9347 7.83657 21.93 7.79002L22 7.74002ZM6.83002 20.5H4.00002V17.67L13.93 7.74002L16.76 10.57L6.83002 20.5ZM18.17 9.16002L15.34 6.33002L16.76 4.92002L19.58 7.74002L18.17 9.16002Z" fill="black"/>
            </svg>
        </button>
    </div>

    <Annotate>
    <div class="question-container">
        {#if showExplanation}
            <div class="explanation-container">
                <P2><p>{@html convertMarkdownItalic(explanation)}</p></P2>
            </div>
        {:else}
            <div class="question">

                <!-- R&W Questions -->

                <div class="vr"></div>
                <hr />

                <div class="question-right">
                    <MarkBarUI
                        i={i - 1} 
                        isSPR={false}
                        toggleElimination={() => isEliminateToolActive = !isEliminateToolActive} 
                        isEliminateTool={isEliminateToolActive}
                        {isMarked}
                        {setMarked}
                    />
                    <!-- MCQ -->
                    <P2>{@html convertMarkdownItalic(question)}</P2>
                    {#each answerChoices as choice, index}
                        <div class="question-choice-wrapper">
                            <button
                                class={getButtonClass(index) + ' question-choice-flex'}
                                onclick={() => handleAnswerSelect(index)}
                                disabled={isAnswered || isCrossedOut[index]}
                            >
                                <div class="question-choice-letter">
                                    <P2>{letters[index]}</P2>
                                </div>
                                <div class="question-choice-text">
                                    {#if choice.length < 200}
                                        <P2>{@html convertMarkdownItalic(choice)}</P2>
                                    {:else}
                                        <P3>{@html convertMarkdownItalic(choice)}</P3>
                                    {/if}
                                </div>
                            </button>
                            {#if isEliminateToolActive}
                            <button
                                class="crossout-btn"
                                aria-label={isCrossedOut[index] ? `Undo cross out for answer ${letters[index]}` : `Cross out answer ${letters[index]}`}
                                onclick={() => handleCrossOut(index)}
                                disabled={isAnswered}
                                type="button"
                            >
                                {#if isCrossedOut[index]}
                                    <P3 isBold={true}><u>Undo</u></P3>
                                {:else}
                                    <span aria-hidden="true" class="cross-choice">{letters[index]}</span>
                                {/if}
                                </button>
                            {/if}
                        </div>
                    {/each}                
                </div>
            </div>
        {/if}
    </div>
    </Annotate>
</div>


{#snippet leftVerbal(questionData)}
    <div class="left-verbal-container max-w-[780px] m-auto flex flex-col gap-4">
        <!-- Figure -->
        {#if questionData.figure}
            <img class="m-auto" style:width={isMath ? "400px" : "90%"} src={questionData.figure} alt="Figure">
        {/if}

        <!-- Student's Notes Question | Fiction Question -->
        {#if questionData.intro}
            <div class="intro">{@html questionData.intro}</div>

            <!-- Student's Notes Question-->
            {#if Array.isArray(questionData.passage)}
                <ul class="ml-10 list-disc">
                    {#each questionData.passage as point}
                        <li>{@html point}</li>            
                    {/each}
                </ul>

            <!-- Fiction Question -->
            {:else}
                <p class="ml-8">{@html questionData.passage}</p>
            {/if}

        <!-- Paired Passage Question -->    
        {:else if questionData.passage2}
            <div class="font-bold text-black">Text 1</div>
            <p>{@html questionData.passage}</p>
            <div class="font-bold text-black mt-6">Text 2</div>
            <p>{@html questionData.passage2}</p>

        <!-- Single Passage Question -->
        {:else}
            <p>{@html questionData.passage}</p>
        {/if}
    </div>
{/snippet}



<style>
    .box-shadow {
        box-shadow: 0.25rem 0.25rem 0px var(--pitch-black, #000);
    }

    .question-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
        flex: 1 1 0%;
        height: 100%;
        padding: 1rem;
        overflow-y: auto;
		scrollbar-color: var(--sky-blue) var(--light-sky-blue);
		scrollbar-width: thin;
    }

    .question {
        width: 100%;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        flex: 1 1 0%;
        height: 100%;
    }

    .passage-wrapper {
        width: 100%;
        display: flex;
        flex-direction: column;
        color: var(--pitch-black, #000);
        line-height: 1.6875rem;
    }

    .vr {
        align-self: stretch;
        width: 1px;
        background: #000;
    }

    hr {
        display: none;
    }

    .question-right {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
        width: 100%;
        padding-bottom: 2rem;
    }

    .question-choice-wrapper {
        display: flex;
        align-items: center;
        width: 100%;
        gap: 1rem;
    }

    .question-choice {
        display: flex;
        align-items: center;
        border: 1px solid #000;
        border-radius: 0.5rem;
        box-shadow: 0.25rem 0.25rem 0px var(--pitch-black, #000);
        background: #FFF;
        width: 100%;
        margin-right: 0.25rem;
    }

    .question-choice:active:enabled {
        box-shadow: none;
        transform: translate(0.25rem, 0.25rem);
    }

    .question-choice-incorrect:disabled {
        background: var(--rose, #EB47AB);
        cursor: not-allowed;
    }

    .question-choice-correct {
        background: var(--aquamarine, #55ECB2);
    }

    .question-choice-selected {
        background: var(--sky-blue, #66E2FF);
    }

    .question-choice-letter {
        display: flex;
        width: 50px;
        padding: 10px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        color: #000;

        line-height: 27px; /* 150% */
    }

    .question-choice-text {
        text-align: start;
        width: 100%;    
        padding: 0.75rem;
        border-left: 1px solid var(--pitch-black, #000);
        line-height: 1.6875rem;
    }

    .crossout-btn {
        height: fit-content;
    }

    .undo-cross {
        text-decoration-line: underline;
    }

    .cross-choice {
        border: 1.5px solid #000;
        border-radius: 50%;

        font-size: 0.9rem;
        font-family: 'Inter';
        font-weight: 600;
        line-height: 25.1px; /* 167.333% */

        width: 1.5rem;
        height: 1.5rem;
        margin: 0 0.52rem;
        display: flex;
        align-items: center;
        justify-content: center;

        position: relative;
    }

    .cross-choice:after {
        content: '';
        position: absolute;
        width: 150%;
        background-color: black;
        height: 2px;
        top: 50%;
        translate: 0 -50%;    
    }

    /* Non-column layout: Individual scrollbars for each side */
    @media (min-width: 768px) and (max-width: 1024px), (min-width: 1200px) {
        .question-container {
            overflow-y: hidden;
        }

        .passage-wrapper {
            padding-bottom: 2rem;
        }
        
        .passage-wrapper {
            height: 100%;
            overflow-y: auto;
            scrollbar-color: var(--sky-blue) var(--light-sky-blue);
            scrollbar-width: thin;
        }
        
        .question-right {
            height: 100%;
            overflow-y: auto;
            scrollbar-color: var(--sky-blue) var(--light-sky-blue);
            scrollbar-width: thin;
        }
    }
    
    /* Switch to column layout on mobile and tablet */
    @media (max-width: 768px), (min-width: 1024px) and (max-width: 1200px) {
        .question {
            flex-direction: column;
            gap: 10px;
        }

        .question-container {
            overflow-y: auto;
            scrollbar-color: var(--sky-blue) var(--light-sky-blue);
            scrollbar-width: thin;
        }

        .passage-wrapper {
            height: auto;
            overflow-y: visible;
        }

        .question-right {
            height: auto;
            overflow-y: visible;
        }

        .vr {
            display: none;
        }

        hr {
            display: block;
            border: 1px solid var(--pitch-black, #000);
            width: 100%;
        }

        .question-right {
            gap: 10px;
        }
    }
    /* Remove padding and border to make it full screen on mobile and tablet */
    @media (max-width: 1024px) {
        .question-right {
            padding-bottom: 2rem;
        }

        .question-wrapper {
            flex: initial;
            height: calc(100vh - 4.5rem);
            box-shadow: none;
            border: none;
            border-radius: 0;
        }

        .question-container {
            padding-bottom: 0;
        }
    }

    /* For extremely big screen */
    @media (min-width: 1440px) {
        .question-wrapper {
            max-width: 73rem;
            max-height: 59rem;
        }
    }

    .question-choice-crossed {
        opacity: 0.4;
        position: relative;
    }

    .question-choice-crossed:after {
        content: '';
        position: absolute;
        width: 104%;
        background-color: black;
        height: 2px;
        top: 50%;
        translate: -1.5% -50%;    
    }

    .crossout-btn {
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: background 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .crossout-btn:disabled, .question-choice:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }

    .question-choice-flex {
        flex: 1 1 0%;
    }
</style>


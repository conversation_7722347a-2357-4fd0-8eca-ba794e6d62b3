<script lang="ts">
	import { H5, P3 } from "$lib/ui";
    import ProgressBar from "./ProgressBar.svelte";

    const { specificSkillScores } = $props();

    let currentIndex = $state(0);

    function setCurrentIndex(index) {
        currentIndex = index;
    }

    const DEFINITION = {
        "Student Notes": "The question lists 4-8 bullet points, and you must choose one to illustrate the student's topic.",
        "Transitions": "Questions that require you to fill in the blank with the most logical transition",
        "Punctuations": "You will be asked to choose among 4 answer choices that are similar except for the punctuation marks they use.",
        "Grammar": "You need to fill in the blank with a grammatically correct choice.",
        "Word in Context": "These questions ask you to use the most appropriate one to fill in the blank or choose the one with the closest meaning to another word.",
        "Reading Comprehension": "These questions ask you about information that are already stated in the passage.",
        "Critical Reading": "These questions give you a long passage and expect you to draw new information in order to solve it.",
        "Synthesis Reading": "These have 2 sources of the information instead of one (a passage and a graph or two passages)",
        "Algebra": "These Math questions mainly concern with equations, inequations, functions, and graphs.",
        "Data Analysis": "Concepts such as probability, statistics, and data modelling will appear in these questions.",
        "Geometry": "You will be tested on common 2D and 3D shapes, as well as some other topics such as trigonometry."
    };

    const TIPS = {
        "Student Notes": [
            "These questions might be intimidatingly long, but you just need to know the right strategy to handle them.",
            "These questions might be intimidatingly long, but you just need to know the right strategy to handle them.",
            "Try to focus on the keywords to work out the correct answer more quickly."
        ],
        "Transitions": [
            "To improve, revise foundational knowledge about types of relationship between clauses.",
            "You have a good base, but transitions are easy to learn, hard to master. Continue practicing!",
            "Take a look at our tips to avoid falling into traps set up by the test makers."
        ],
        "Punctuations": [
            "Try to become familiar with concepts of clauses and basic punctuation rules.",
            "Your English is good. Try to learn basic punctuation rule systematically.",
            "Look for common motifs that the test makers use to avoid falling into traps."
        ],
        "Grammar": [
            "Revise the basic convention of English grammar.",
            "Consume more English contents (articles, books, videos, podcasts,...) to improve your fluency.",
            "Refine your knowledge to avoid falling into traps."
        ],
        "Word in Context": [
            "Try to build your vocab base to comprehend the context of the passage.",
            "Learn to infer the meaning of an unfamiliar word from contextual clues.",
            "Learn to identify the meaning of a new word from its origin or from its second meaning."
        ],
        "Reading Comprehension": [
            "Read the question caresully to identify the information you need to look for.",
            "Learn to scan the topic and track the ideas of the passage quickly",
            "Manage time better with lexical inferencing (understanding a text by reading only 50-70%)."
        ],
        "Critical Reading": [
            "Learn to summarize a long text and extract key information.",
            "Learn to make an inference and train your critical and logical thinking.",
            "Use process of elimination to avoid falling into the test makers' traps."
        ],
        "Synthesis Reading": [
            "Learn to manage two sources of information at a time.",
            "Learn to make an educated guess on the information you need to look for in the correct answer.",
            "Use one source of information at a time to rule our incorrect answer choices."
        ],
        "Algebra": [
            "Revise basic concepts of equations, functions and graphs.",
            "Learn to use DESMOS to assist with your calculation and visualization (for graphs).",
            "Analyze your mistakes and read the explanation carefully to learn from experience."
        ],
        "Data Analysis": [
            "Learn the basics of probability, statistics and data modeling in English if it's not your mother tongue.",
            "Read the question (at the end) carefully to avoid traps, especially for word problems.",
            "For lengthy questions, learn to track the infomation as you speed read to not get confused."
        ],
        "Geometry": [
            "Review basic shapes, properties and formulas.",
            "Get creative and try to apply, even combine formulas in various way to solve a problem.",
            "Double check your answers and remember to read the questions carefully"
        ],
    };
</script>
<div class="chart-container border border-black rounded-lg bg-[var(--light-tangerine)] flex font-['Inter'] overflow-hidden h-[350px]">
    <div class="skill-buttons-section h-full flex-1 flex flex-col ">
        {#each specificSkillScores as skill, index}
        <button class="p-4 flex-1 flex flex-col gap-2 overflow-hidden hover:bg-[#fff3a8]" style:border-bottom={index === 3 ? "none" : "1px solid var(--pitch-black)"} class:bg-[var(--light-yellow)]={currentIndex === index} onclick={() => setCurrentIndex(index)}>
            <div class="button-head flex justify-between">
                <div class="font-semibold">{skill.questionType}</div>
                <p class="relative text-black"><span class="text-[#07cbf7] font-semibold">{Math.round(skill.correct / skill.total * 100)}%</span> accuracy</p>
            </div>
            
            <ProgressBar percentage={skill.correct / skill.total * 100} />
        </button>
        {/each}
    </div>
    <div class="assessment-section bg-[var(--light-yellow)] border-solid border-black border-l flex-1 flex flex-col gap-4 py-4 px-6 items-center overflow-auto">
        <div class="assessment-heading flex flex-col items-center gap-2">
            <P3>Question Type</P3>
            <H5>{specificSkillScores[currentIndex].questionType}</H5>
        </div>
        <P3>You got {specificSkillScores[currentIndex].correct}/{specificSkillScores[currentIndex].total} questions correct</P3>
        <p class="text-center">{DEFINITION[specificSkillScores[currentIndex].questionType]}</p>
        <p class="text-center"><span class="font-semibold">Tip:</span> {TIPS[specificSkillScores[currentIndex].questionType][Math.floor(Math.random() * 3)]}</p>
    </div>
</div>

<style>
    .chart-container {
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        margin: 4px 0;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>
<!--
  @component
  ## CircularProgressBar
  A circular progress bar component for simulation analysis that displays progress with customizable colors and text.

  ## Props
    - `score` (number): The current score/progress value
    - `total` (number): The maximum score/total value
    - `text` (string): The text to be displayed in the center of the progress bar
    - `size` (number): The size of the progress bar in pixels (default: 130)
    - `showPercentage` (boolean): Whether to show percentage instead of fraction (default: false)
    - `primaryColor` (string): Primary color for the background circle
    - `secondaryColor` (string): Secondary color for the progress circle

  ## Usage
  ```html
  <CircularProgressBar
    score={75}
    total={100}
    text="Progress"
    size={150}
    showPercentage={true}
    primaryColor="var(--sky-blue)"
    secondaryColor="var(--light-sky-blue)"
  />
  ```
-->

<script lang="ts">
  interface Props {
    score: number;
    total: number;
    text: string;
    size?: number;
    showPercentage?: boolean;
    primaryColor?: string;
    secondaryColor?: string;
  }

  let {
    score,
    total,
    text,
    size = 130,
    showPercentage = false,
    primaryColor = "var(--sky-blue)",
    secondaryColor = "var(--light-sky-blue)"
  }: Props = $props();

  // Calculate percentage and ensure it's between 0 and 1
  let percentage = $derived(Math.min(Math.max(score / total, 0), 1));

  // SVG circle calculations
  const stroke = 13;
  const center = size / 2;
  const radius = (size - stroke) / 2;
  const circumference = 2 * Math.PI * radius;

  // Shorten text if needed for better display
  let displayText = $derived(text.length > 12 ? text.substring(0, 10) + "..." : text);
</script>

<div class="circle-wrapper">
  <div class="box-shadow-wrapper">
    <svg
      class="svg"
      width={size}
      height={size}
      viewBox="0 0 {size + 4} {size + 4}"
      style="--center: {center}px; --primary-color: {primaryColor}; --secondary-color: {secondaryColor}"
    >
      <!-- Background circle -->
      <circle
        class="bg"
        cx={center}
        cy={center}
        r={radius}
        fill="none"
        stroke="var(--primary-color)"
        stroke-width={stroke}
      />

      <!-- Border circle for visual depth -->
      <circle
        class="border"
        cx={center}
        cy={center}
        r={radius}
        fill="none"
        stroke="var(--pitch-black)"
        stroke-width={stroke}
        stroke-dasharray={`${circumference * (1 - percentage) + 4} ${circumference * percentage - 4}`}
      />

      <!-- Progress circle -->
      <circle
        class="fg"
        cx={center}
        cy={center}
        r={radius}
        fill="none"
        stroke="var(--secondary-color)"
        stroke-width={stroke + 0.3}
        stroke-dasharray={`${circumference * (1 - percentage)} ${circumference * percentage}`}
      />
    </svg>

    <!-- Score/percentage display -->
    <div class="fraction">
      {#if showPercentage}
        <span>{Math.round(percentage * 100)}%</span>
      {:else}
        <span>{score}/{total}</span>
      {/if}
    </div>
  </div>

  <!-- Center text -->
  <div class="middle-text">
    {displayText}
  </div>
</div>

<style>
  .circle-wrapper {
    position: relative;
    display: flex;
    width: fit-content;
  }

  /* Progress circle styling */
  circle.fg {
    rotate: -90deg;
    transform-origin: var(--center) var(--center);
    transition: stroke-dasharray 0.3s ease-in-out;
  }

  circle.border {
    rotate: -91.7deg;
    transform-origin: var(--center) var(--center);
  }

  /* SVG border effect using drop-shadow */
  .svg {
    filter: drop-shadow(-1px 0 var(--pitch-black))
            drop-shadow(0 -1px var(--pitch-black))
            drop-shadow(0 1px var(--pitch-black))
            drop-shadow(1px 0 var(--pitch-black));
  }

  /* Box shadow for the entire component */
  .box-shadow-wrapper {
    filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
  }

  /* Center text styling */
  .middle-text {
    font-family: "Inter";
    font-size: 1.125rem;
    font-weight: 600;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translate(0, -50%);
    width: 100%;
    text-align: center;
    color: var(--pitch-black);
    pointer-events: none;
  }

  /* Score/percentage display */
  .fraction {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.25rem;
    border: 1px solid var(--pitch-black);
    border-radius: 0.25rem;
    position: absolute;
    right: 2px;
    bottom: 18px;
    min-width: 47px;
    min-height: 25px;
    background-color: white;
    font-family: "Inter";
    font-size: 0.875rem;
    font-weight: 450;
    color: var(--pitch-black);
  }

  /* Responsive adjustments */
  @media (max-width: 540px) {
    .middle-text {
      font-size: 1rem;
    }

    .fraction {
      font-size: 0.75rem;
      min-width: 40px;
      min-height: 22px;
    }
  }
</style>
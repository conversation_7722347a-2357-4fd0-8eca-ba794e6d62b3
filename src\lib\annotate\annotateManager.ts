export const colors = {
    yellow: { normal: "#fff59d", focus: "#ffc739" },
    pink:   { normal: "#f8bbd0", focus: "#f48fb1" },
    blue:   { normal: "#b3e5fc", focus: "#4fc3f7" },
};

export class Annotate {
    root: HTMLElement;
    positions: number[];
    marks: { [key: string]: Mark };
    highlights: { [key: string]: Highlight };
    selectedHighlight: Highlight;
    hoveredHighlight: Highlight;

    constructor(rootElement: HTMLElement = null) {
        this.root = rootElement;
        this.positions = [];
        this.marks = {};
        this.highlights = {};
    }

    // Get the total offset of a target node and offset within the root node
    getTotalOffset(targetNode, targetOffset) {
        const iterator = document.createNodeIterator(this.root, NodeFilter.SHOW_TEXT);
        let currentNode;
        let totalOffset = 0;
        while ((currentNode = iterator.nextNode())) {
            if (currentNode === targetNode) return totalOffset + targetOffset;
            totalOffset += currentNode.textContent.length;
        }
        return -1; // Not found
    }

    // Get the Range of a given Start and End Position within the Root Node
    getRange(pos: [number, number]) {
        if (!pos) return null;
        const [startPos, endPos] = pos;
        let charCount = 0;
        let startNode, startNodeOffset;
        let endNode, endNodeOffset;
    
        const iterator = document.createNodeIterator(this.root, NodeFilter.SHOW_TEXT);
    
        let currentNode;
        while ((currentNode = iterator.nextNode())) {
            const nextCharCount = charCount + currentNode.textContent.length;
            if (!startNode && startPos >= charCount && startPos < nextCharCount) {
                startNode = currentNode;
                startNodeOffset = startPos - charCount;
            }
            if (!endNode && endPos > charCount && endPos <= nextCharCount) {
                endNode = currentNode;
                endNodeOffset = endPos - charCount;
                break;
            }
            charCount = nextCharCount;
        }
    
        const range = document.createRange();
        range.setStart(startNode, startNodeOffset);
        range.setEnd(endNode, endNodeOffset);

        return range;
    }

    splitRangeToTextNode(pos: [number, number]) {
        if (pos === null) return null;
        const [startPos, endPos] = pos;
        let charCount = 0;
        let startNode, startNodeOffset;
        let endNode, endNodeOffset;
    
        const iterator = document.createNodeIterator(this.root, NodeFilter.SHOW_TEXT);

        const textNodes = [];
    
        let currentNode;
        while ((currentNode = iterator.nextNode())) {
            if (endNode) break;
            const nextCharCount = charCount + currentNode.textContent.length;

            if (!startNode && startPos >= charCount && startPos < nextCharCount) {
                startNode = currentNode;
                startNodeOffset = startPos - charCount;
            }
            if (!endNode && endPos > charCount && endPos <= nextCharCount) {
                endNode = currentNode;
                endNodeOffset = endPos - charCount;
            }

            if (startNode) textNodes.push([Math.max(startPos, charCount), Math.min(endPos, nextCharCount)]);
            charCount = nextCharCount;
        }

        return textNodes;
    }
    
    // Create a mark element with the given start and end offsets within the root node
    createMark(pos: [number, number]): Mark {
        const markKey = `${pos[0]},${pos[1]}`;

        const range = this.getRange(pos);
        const markElement = document.createElement("mark");
        range.surroundContents(markElement);

        const mark = new Mark(markElement);

        // Add hover event
        markElement.addEventListener("mouseover", () => {
            this.updateHoverHighlight(mark.minHighlight, true);
        });
        markElement.addEventListener("mouseout", () => {
            this.updateHoverHighlight(mark.minHighlight, false);
        });

        // Add click event
        markElement.addEventListener("click", () => {
            const selection = document.getSelection();
            if (selection.rangeCount && !selection.isCollapsed) return;

            this.setFocus(mark.minHighlight);
        });

        this.marks[markKey] = mark;
        return mark;
    }

    
    // Remove a mark element with the given start and end offsets within the root node
    removeMark(markPos: [number, number]) {  
        const markKey = `${markPos[0]},${markPos[1]}`;
        this.marks[markKey].delete();
        delete this.marks[markKey];
    }

    // Split the mark element into two marks at the given position
    splitMarks(markPos: [number, number], position) {
        const splittedMark = this.marks[`${markPos[0]},${markPos[1]}`];
        this.removeMark(markPos);
        this.createMark([markPos[0], position]).setMark(splittedMark);
        this.createMark([position, markPos[1]]).setMark(splittedMark);
    }

    // Merge two mark elements into one
    mergeMarks(markPos: [number, number], position) {
        let mergedMark = this.marks[`${markPos[0]},${position}`];
        this.removeMark([markPos[0], position]);
        this.removeMark([position, markPos[1]]);
        this.createMark(markPos).setMark(mergedMark);
    }


    // Normalize the positions array and the marks within the root node
    normalizeMarks(startIndex, endIndex) {
        // Remove marks that have no highlights
        let removeCount = 0;

        this.positions = this.positions.filter((cur, i, positions) => {
            if (i < startIndex || i > endIndex) return true;

            const prev = positions[i - 1];
            const next = positions[i + 1];

            if (i !== endIndex) {
                const mark = this.marks[`${cur},${next}`];
                if (mark.highlights.length === 0) {
                    this.removeMark([cur, next]);
                    removeCount++;
                }
            }

            const hasPrev = prev !== undefined && `${prev},${cur}` in this.marks;
            const hasNext = next !== undefined && `${prev},${cur}` in this.marks;

            return hasPrev || hasNext;
        });

        endIndex -= removeCount;

        // Merge marks that belong to same highlights and have the same parent
        let prev = null;
        for (let i = startIndex - 1; i <= endIndex; i++) {
            const cur = this.positions[i];
            const next = this.positions[i + 1];
            if (cur === undefined || next === undefined || !(`${cur},${next}` in this.marks)) {
                prev = null;
                continue;
            }

            if (prev === null) {
                prev = cur;
                continue;
            }

            const mark1 = this.marks[`${prev},${cur}`];
            const mark2 = this.marks[`${cur},${next}`];
            if (mark1.equal(mark2)) {
                this.removeMark([prev, cur]);
                this.removeMark([cur, next]);
                this.createMark([prev, next]).setMark(mark1);
                this.positions.splice(i, 1);
                i--;
                endIndex--;
            } else {
                prev = cur;
            }
        }

        // this.normalizeDeep(this.root);
        this.root?.normalize();
    }

    // Remove a highlight from the mark element
    removeHighlight(highlight: Highlight) {
        const key = `${highlight.pos[0]},${highlight.pos[1]}`;
        delete this.highlights[key];

        let startIndex = this.positions.findIndex(x => x === highlight.pos[0]);
        let endIndex = this.positions.findIndex(x => x === highlight.pos[1]);
        for (let i = startIndex; i < endIndex; i++) {
            this.marks[`${this.positions[i]},${this.positions[i + 1]}`].removeHighlight(highlight);
        }

        this.normalizeMarks(startIndex, endIndex);
    }

    // Create new highlight
    createHighlight(highlight: Highlight) {
        const [startPos, endPos] = highlight.pos;
        let startIndex = this.positions.findIndex(x => x >= startPos);
        let newStartPos = true;

        if (startIndex === -1) {
            this.positions.push(startPos);
            startIndex = this.positions.length - 1;
        } else if (this.positions[startIndex] !== startPos) {
            this.positions.splice(startIndex, 0, startPos);
        } else {
            newStartPos = false;
        }

        if (newStartPos && startIndex > 0 && startIndex < this.positions.length - 1 && `${this.positions[startIndex - 1]},${this.positions[startIndex + 1]}` in this.marks) {
            this.splitMarks([this.positions[startIndex - 1], this.positions[startIndex + 1]], startPos);
        }

        let endIndex = this.positions.findIndex(x => x >= endPos);
        let newEndPos = true;

        if (endIndex === -1) {
            this.positions.push(endPos);
            endIndex = this.positions.length - 1;
        } else if (this.positions[endIndex] !== endPos) {
            this.positions.splice(endIndex, 0, endPos);
        } else {
            newEndPos = false;
        }

        if (newEndPos && endIndex < this.positions.length - 1 && `${this.positions[endIndex - 1]},${this.positions[endIndex + 1]}` in this.marks) {
            this.splitMarks([this.positions[endIndex - 1], this.positions[endIndex + 1]], endPos);
        }

        const textNodes = this.splitRangeToTextNode(highlight.pos);
        textNodes.forEach(pos => {
            const addedIndex = this.positions.findIndex(x => x >= pos[0]);
            if (this.positions[addedIndex] !== pos[0]) {
                this.positions.splice(addedIndex, 0, pos[0]);
                endIndex++;
            }
        })

        for (let i = startIndex; i < endIndex; i++) {
            const markKey = `${this.positions[i]},${this.positions[i + 1]}`;
            const mark = this.marks[markKey] ?? this.createMark([this.positions[i], this.positions[i + 1]]);
            mark.addHighlight(highlight);
        }
    }

    // Highlight the selected text
    highlight(range: Range, color: string = "yellow"): boolean {
        // Get the total offset of the start and end of the range
        const startPos = this.getTotalOffset(range.startContainer, range.startOffset);
        const endPos = this.getTotalOffset(range.endContainer, range.endOffset);

        const newHighlight = new Highlight([startPos, endPos], color, "");

        this.highlights[`${startPos},${endPos}`] = newHighlight;
        this.createHighlight(newHighlight);
        this.setFocus(newHighlight);

        // Collapse the range to the end
        range.collapse(false);
        return true;
    }

    updateHoverHighlight(highlight: Highlight, hover: boolean) {
        if (!highlight) return;
        this.hoveredHighlight = hover ? highlight : null;
        this.positions.forEach((pos, i) => {
            if (pos < highlight.pos[0] || pos >= highlight.pos[1]) return;
            const tempMark = this.marks[`${pos},${this.positions[i + 1]}`];

            tempMark.setElementColor(hover ? colors[highlight.color].focus : tempMark.color);
        });
    }

    updateSelectedHighlight(highlight: Highlight, selected: boolean) {
        if (!highlight) return;
        this.positions.forEach((pos, i) => {
            if (pos < highlight.pos[0] || pos >= highlight.pos[1]) return;
            const tempMark = this.marks[`${pos},${this.positions[i + 1]}`];

            tempMark.updateColor(selected ? colors[highlight.color].focus : colors[tempMark.minHighlight.color].normal);
        });
    }

    setFocus(highlight: Highlight) {
        this.updateSelectedHighlight(this.selectedHighlight, false);
        this.selectedHighlight = highlight;
        this.updateSelectedHighlight(this.selectedHighlight, true);
    }

    changeHighlightColor(highlight: Highlight, color: string) {
        if (!highlight) return;
        highlight.color = color;

        this.positions.forEach((pos, i) => {
            if (pos < highlight.pos[0] || pos >= highlight.pos[1]) return;
            this.marks[`${pos},${this.positions[i + 1]}`].updateColor(color);
        });
    }

    getNoteText(highlight: Highlight) {
        if (!highlight) return null;
        return highlight.note;
    }

    setNoteText(highlight: Highlight, text: string) {
        highlight.note = text;
    }


    rerenderHighlights() {
        this.positions.forEach((cur, i) => {
            if (i === this.positions.length - 1) return;
            if (`${cur},${this.positions[i + 1]}` in this.marks)
                this.createMark([cur, this.positions[i + 1]]);
        });
    }

    setRoot(rootElement: HTMLElement) {
        this.root = rootElement;
        this.rerenderHighlights();
    }

    dispose() {
        for (const mark of Object.values(this.marks)) mark.delete();
    }
}


class Highlight {
    pos: [number, number];
    color: string;
    note: string;

    constructor(pos: [number, number], color: string, note: string) {
        this.pos = pos;
        this.color = color;
        this.note = note;
    }

    length() {
        return this.pos[1] - this.pos[0];
    }

    lt(other: Highlight) {
        return this.length() < other.length(); 
    }

    is(other: Highlight) {
        return this.pos[0] === other.pos[0] && this.pos[1] === other.pos[1];
    }
}


class Mark {
    element: HTMLElement;
    highlights: Highlight[];
    color: string;
    minHighlight: Highlight;

    constructor(element: HTMLElement) {
        this.element = element;
        this.highlights = [];
        this.color =  null;
        this.minHighlight = null;
    }

    setMark(oldMark: Mark) {
        if (!oldMark) return;
        this.highlights = structuredClone(oldMark.highlights);
        this.updateColor(oldMark.color);
        this.minHighlight = structuredClone(oldMark.minHighlight);
    }

    updateMinHighlight() {
        if (!this.highlights.length) this.minHighlight = null;
        else {
            this.minHighlight = this.highlights.reduce((min: Highlight, item: Highlight) => min.lt(item) ? min : item, this.highlights[0]);
            this.updateColor(colors[this.minHighlight.color].normal);
        }
    }

    addHighlight(highlight: Highlight) {
        this.highlights.push(highlight);
        this.updateMinHighlight();
    }

    removeHighlight(highlight: Highlight) {
        this.highlights = this.highlights.filter(item => !item.is(highlight));
        this.updateMinHighlight();
    }

    updateColor(color: string) {
        this.color = color;
        this.setElementColor(color);
    }

    setElementColor(color: string) {
        this.element.style.backgroundColor = color;
    }

    equal(other: Mark) {
        return this.element.parentNode === other.element.parentNode && this.highlights.length === other.highlights.length && this.highlights.every(item => other.highlights.some(item2 => item[0] === item2[0] && item[1] === item2[1]));
    }

    delete() {
        const parent = this.element.parentNode;
        while (this.element.firstChild) {
            parent.insertBefore(this.element.firstChild, this.element);
        }
        parent.removeChild(this.element);
    }
}
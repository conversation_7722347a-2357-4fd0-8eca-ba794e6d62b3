<script lang="ts">
    // Timer state
	let seconds = $state(0);
	let minutes = $state(0);

	// Format time as MM:SS
	let formattedTime = $derived(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);

    let timerInterval = null;

	function startTimer() {
		if (timerInterval) return;

		timerInterval = setInterval(() => {
			seconds++;
			if (seconds >= 60) {
				seconds = 0;
				minutes++;
			}
		}, 1000);
	}

	function stopTimer() {
		if (timerInterval) {
			clearInterval(timerInterval);
			timerInterval = null;
		}
	}

	function resetTimer() {
		stopTimer();
		seconds = 0;
		minutes = 0;
	}
</script>

{formattedTime}
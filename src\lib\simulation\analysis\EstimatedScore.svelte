<script lang="ts">
    import ScoreChange from "./ScoreChange.svelte";

    const { studentData, studentLastAttempt } = $props();
</script>

<div class="estimated-score-container flex flex-col items-center justify-center p-4 border border-black bg-[var(--light-yellow)] rounded-lg font-['Inter'] h-full flex-1">
    <p class="text-2xl font-semibold">Your Estimated Score</p>
    <div class="score">{studentData.predictedTotalScore}</div> 
    <ScoreChange scoreChange={studentLastAttempt ? studentData.predictedTotalScore - studentLastAttempt.predictedTotalScore : studentData.predictedTotalScore}/>
    {#if studentLastAttempt}
        <p class="mt-2 text-sm">Since last Simulation</p>
    {:else}
        <div class="flex gap-2 items-center"> 
            <svg class="translate-y-[3px]" xmlns="http://www.w3.org/2000/svg" fill="#00d154" width="20" height="20" viewBox="0 0 256 256" id="Flat">
                <path d="M208.8584,144a15.85626,15.85626,0,0,1-10.46778,15.01367l-52.16015,19.2168-19.2168,52.16015a16.00075,16.00075,0,0,1-30.02734,0l-19.2168-52.16015-52.16015-19.2168a16.00075,16.00075,0,0,1,0-30.02734l52.16015-19.2168,19.2168-52.16015a16.00075,16.00075,0,0,1,30.02734,0l19.2168,52.16015,52.16015,19.2168A15.85626,15.85626,0,0,1,208.8584,144ZM152,48h16V64a8,8,0,0,0,16,0V48h16a8,8,0,0,0,0-16H184V16a8,8,0,0,0-16,0V32H152a8,8,0,0,0,0,16Zm88,32h-8V72a8,8,0,0,0-16,0v8h-8a8,8,0,0,0,0,16h8v8a8,8,0,0,0,16,0V96h8a8,8,0,0,0,0-16Z"/>
            </svg>
            <p class="mt-2 text-sm text-[#00d154]">First Simulation</p>
        </div>
    {/if}

    <div class="score-components flex flex-col gap-3 mt-8">
        <div class="component flex items-center gap-3">
            <p class="component-score font-bold text-5xl text-[var(--sky-blue)]">{studentData.predictedVerbalScore}</p>
            <div class="flex flex-col">
                <p class="component-name font-semibold text-xl">Reading & Writing</p>
                <ScoreChange scoreChange={studentLastAttempt ? studentData.predictedVerbalScore - studentLastAttempt.predictedVerbalScore : studentData.predictedVerbalScore}/>
            </div>
        </div>
        <div class="component flex items-center gap-3">
            <p class="component-score font-bold text-5xl text-[var(--rose)]">{studentData.predictedMathScore}</p>
            <div class="flex flex-col">
                <p class="component-name font-semibold text-xl">Math</p>
                <ScoreChange scoreChange={studentLastAttempt ? studentData.predictedMathScore - studentLastAttempt.predictedMathScore : studentData.predictedMathScore}/>
            </div>
        </div>
    </div>
</div>


<style>
    .estimated-score-container {
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .score {
        width: fit-content;
        font-size: 5.625rem;
        font-family: "Inter";
        font-weight: 800;
        -webkit-text-stroke: var(--pitch-black) 2px;
        paint-order: stroke fill;

        background: linear-gradient(90deg, var(--sky-blue) 0%, var(--rose) 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        -webkit-filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
        filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
    }
</style>
<script lang="ts">
    import Simulation from "$lib/simulation/Simulation.svelte";
	import { P2 } from "$lib/ui";
	import { onMount } from "svelte";
    import { page } from '$app/state';
    import { user } from '$lib/firebase/auth.svelte.ts';

    let simulationId = $derived(page.params.simulationId);

    let { data } = $props();

    let modules = $state(null);

    async function getSimulationData() {
        const simulationData = await fetch('/api/simulation/get-simulation-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ simulationId })
        });

        const { data, success, message } = await simulationData.json();

        if (!success) {
            alert(`Error fetching simulation data: ${message}`);
            return null;
        }
        
        return data.modules;
    }

    onMount(async () => {
        modules = await getSimulationData();
    })
</script>

{#if modules}
    <Simulation {simulationId} {modules} uid={$user.uid}/>
{:else}
    <div class="fixed inset-0 loading-container flex items-center justify-center gap-4">
        <div class="loading-spinner w-8 h-8 border-4 border-[var(--light-sky-blue)] border-t-[var(--sky-blue)] rounded-full animate-spin"></div>
        <P2>Loading Simulation...</P2>
    </div>
{/if}


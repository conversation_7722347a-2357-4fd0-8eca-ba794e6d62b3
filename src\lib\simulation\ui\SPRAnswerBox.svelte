<script module lang="ts">
    declare const katex: any;
</script>

<script lang="ts">
	import { onMount } from "svelte";

    let { 
        currentQuestion = null,
        currentQuestionState,
        setAnswer = null,
        isWalkthrough = false
    } = $props();

    let studentAnswers = currentQuestionState.answer;

    let inputElement: HTMLInputElement = $state();
    let inputValue = $state(studentAnswers ?? '');

    let latexValue = $derived(inputValue.includes('/') ? inputValue.replace('/', '\\over') : inputValue);

    const allowedChars = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.', '-', '/'];
    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

    function checkInput(e: KeyboardEvent) {
        if (allowedKeys.includes(e.key)) {
            return;
        }

        if (!allowedChars.includes(e.key)) {
            e.preventDefault();
        }

        if (inputValue.length && e.key === '-') {
            e.preventDefault();
        }

        if (inputValue.length === 5 + isNegative) {
            e.preventDefault();
        }

        if (e.key === '/' && (inputValue.length === Number(isNegative) || inputValue.includes('/') || inputValue[inputValue.length - 1] === '.')) {
            e.preventDefault();
        }

        if (e.key === '.' && (inputValue.length > 0 && inputValue[inputValue.length - 1] === '.')) {
            e.preventDefault();
        }
    }

    let isNegative = $derived(inputValue.startsWith('-'));

    let displayedElement: HTMLSpanElement = $state();

    $effect(() => {
        if (isWalkthrough) return;
        setAnswer(inputValue ? inputValue : null);
        katex.render(latexValue, displayedElement, {
            throwOnError: false
        });
    })

    onMount(() => {
        if (isWalkthrough) {
            if (studentAnswers) {
                katex.render(latexValue, displayedElement, {
                    throwOnError: false
                });
            }
            return;
        }
        inputElement?.focus();
    });
</script>

<svelte:head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css" integrity="sha384-5TcZemv2l/9On385z///+d7MSYlvIEw9FuZTIdZ14vJLqWphw7e7ZPuOiCHJcFCP" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.js" integrity="sha384-cMkvdD8LoxVzGF/RPUKAcvmm49FQ0oxwDF3BGKtDXcEc+T1b2N+teh/OJfpU0jr6" crossorigin="anonymous"></script>
</svelte:head>


<div class="answer-box-container flex flex-col gap-7">
    <!-- Answer box -->
    <div class="answer-box h-[57px] flex items-center justify-center border border-[#333333] border-solid rounded-lg px-2" style:width={isNegative ? "120px" : "102px"} class:bg-[#D1FFEE]={isWalkthrough && currentQuestionState.isCorrect} class:bg-[#FFDAF1]={isWalkthrough && currentQuestionState.answer && !currentQuestionState.isCorrect}>
        <input class="answer-input border-b border-[#333333] border-solid w-full outline-none tracking-wider text-[28px]" style:font-family="monospace" type="text" bind:this={inputElement} bind:value={inputValue} onkeydown={checkInput} disabled={isWalkthrough}>
    </div>

    <!-- Answer Preview -->
    <div class="answer-preview font-bold text-xl">
        Answer Preview: <span bind:this={displayedElement}></span>
    </div>

    {#if isWalkthrough}
    <!-- Correct Answer -->
    <div class="correct-answer font-bold text-xl">
        Correct Answer: ${currentQuestion.correctAnswer}$
    </div>
    {/if}
</div>
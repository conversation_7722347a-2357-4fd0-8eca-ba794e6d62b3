import { adminDB } from '$lib/server/admin.ts';
import { supabase } from "$lib/server";
import { json } from '@sveltejs/kit';


export const GET = async ({ locals }) => {
    const { data: simulationsData, error: simulationsError } = await supabase
        .from('simulations')
        .select('id, title')
        .order('id', { ascending: true });
    
    if (simulationsError) return json({ success: false, message: simulationsError.message });

    await Promise.all(simulationsData.map(async (simulation) => {
        const simulationRef = adminDB.doc(`users/${locals.uid}/simulations/${simulation.id}`);
        const simulationSnap = (await simulationRef.get()).data();
        (simulation as any).progress = simulationSnap ?? null;
    }));

    return json({ data: simulationsData, success: true });
}
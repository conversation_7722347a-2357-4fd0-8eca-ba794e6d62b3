import { adminDB } from '$lib/server/admin.ts';
import { json } from '@sveltejs/kit';

export const POST = async ({ request, locals }) => {
    const { simulationId, modules } = await request.json();

    const attemptList = await adminDB.collection(`users/${locals.uid}/simulations/${simulationId}/attempts`).orderBy("submitTime", "desc").get();
    
    const studentCurrentAttempt = attemptList.docs[0].data();
    const studentLastAttempt = attemptList.docs[1]?.data() ?? null;

    // Remove submitTime from studentCurrentAttempt and studentLastAttempt
    delete studentCurrentAttempt.submitTime;
    if (studentLastAttempt) delete studentLastAttempt.submitTime;
    
    const skillScores = {
        "Reading": [
            {
                questionType: "Word in Context",
                subTypes: ["Word in Context"],
                total: 0,
                correct: 0
            },
            {
                questionType: "Reading Comprehension",
                subTypes: ["Specific Detail", "Main Purpose", "Main Idea", "Overall Structure", "Main Purpose Underlined"],
                total: 0,
                correct: 0
            },
            {
                questionType: "Critical Reading",
                subTypes: ["Sentence Completion", "Illustrate/Support/Undermine", "Command of Evidence", "Inference"],
                total: 0,
                correct: 0
            },
            {
                questionType: "Synthesis Reading",
                subTypes: ["Paired Passage", "Graph & Chart"],
                total: 0,
                correct: 0
            }
        ],
        "Writing": [
            {
                questionType: "Transitions",
                subTypes: ["Transitions"],
                total: 0,
                correct: 0
            },
            {
                questionType: "Student Notes",
                subTypes: ["Student Notes"],
                total: 0,
                correct: 0
            },
            {
                questionType: "Punctuations",
                subTypes: ["Punctuation"],
                total: 0,
                correct: 0
            },
            {
                questionType: "Grammar",
                subTypes: ["Grammar"],
                total: 0,
                correct: 0
            }   
        ],
        "Math": [
            {
                questionType: "Algebra",
                total: 0,
                correct: 0
            },
            {
                questionType: "Data Analysis",
                total: 0,
                correct: 0
            },
            {
                questionType: "Geometry",
                total: 0,
                correct: 0
            }
        ]
    };

    ["Reading", "Writing"].forEach((section) => {
        skillScores[section].forEach((skill) => {
            modules.forEach((module) => {
                module.questions.forEach((question, index) => {
                    if (skill.subTypes.includes(question.questionType)) {
                        skill.total++;
                        if (studentCurrentAttempt.answers[module.id][index].isCorrect) {
                            skill.correct++;
                        }
                    }
                });
            });
        });
    });

    skillScores["Math"].forEach((skill) => {
        modules.forEach((module) => {  
            if (!["M1", "M2"].includes(module.id)) return; 
            module.questions.forEach((question, index) => {
                if (skill.questionType === question.topic) {
                    skill.total++;
                    if (studentCurrentAttempt.answers[module.id][index].isCorrect) {
                        skill.correct++;
                    }
                }
            });
        });
    });

    return json({ success: true, data: { studentCurrentAttempt, studentLastAttempt, skillScores }});
}
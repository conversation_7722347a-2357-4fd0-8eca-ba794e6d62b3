<!-- 
    @component
    ## SimMid
    A component to display the middle section of the simulation test.
-->
<script module lang="ts">
    declare global {
        interface Window {
            MathJax: any;
        }
    }
</script>

<script lang='ts'>
	import * as UI from "../ui";
    import { AnnotateManager } from "$lib/annotate/annotateManager";
	import { untrack } from "svelte";
	import ResizeWrapper from "../ui/ResizeWrapper.svelte";

    let {
        currentQuestion,
        currentQuestionState,
        setAnswer,
        setMarked,
        setCrossed,
        isMath,
        isCalculatorOpen,
        annotateManager = $bindable(),
        currentModuleIndex,
        currentModuleLength,
        currentQuestionIndex
    } = $props();

    let isSPR = $derived(!(currentQuestion.answerChoices));

    // Elimination tool
    let isEliminateTool = $state(false);

    function toggleElimination() {
        isEliminateTool = !isEliminateTool;
    }

    // Annotate
    let middleSection: HTMLElement = $state();

    // Reinitialize Annotate Manager when module change
    $effect(() => {
        currentModuleIndex;
        untrack(() => {
            annotateManager = Array(currentModuleLength).fill(null).map(() => new AnnotateManager([middleSection]));
        });
    })

    // Update Annotate Manager when question change
    $effect(() => {
        annotateManager[currentQuestionIndex].setRoot([middleSection]);
    })

    // Render Latex again each time changing question
    $effect(() => {
        if (middleSection) {
            window.MathJax = {
                tex: {
                    inlineMath: [["$", "$"], ["\\(", "\\)"]],
                    displayMath: [["$$", "$$"], ["\\[", "\\]"]]
                },
                svg: { fontCache: "global" }
            };

            let script = document.createElement('script');
            script.src = "https://cdn.jsdelivr.net/npm/mathjax@4/tex-chtml.js";
            document.head.append(script);
        }
    });

    let currentResizeState = $state(50);
    function handleResize(pos: number) {
        currentResizeState = pos;
    }
</script>

<!-- svelte-ignore a11y_no_static_element_interactions -->
{#key currentQuestion}
<div class="middle flex w-full h-full gap-[5px] justify-center leading-relaxed font-['Merriweather'] text-[#333]" bind:this={middleSection}>
    {#if isMath && !isSPR}
        <div class="middle-math flex-1 p-10 transition-transform duration-200 overflow-y-auto" class:translate-x-[200px]={isCalculatorOpen}>
            {@render question()}
        </div>
    {:else}
        <ResizeWrapper limit={460} {handleResize} {currentResizeState}>
        {#snippet left()}
        <div class="left p-10">
            {#if isSPR}
                {@render leftSPR()}
            {:else}
                {@render leftVerbal(currentQuestion)}
            {/if}
        </div>    
        {/snippet}
        
        {#snippet right()}
        <div class="right flex-1 p-10">
            {@render question()}
        </div>
        {/snippet}
        </ResizeWrapper>
    {/if}
</div>
{/key}

{#snippet leftSPR()}
{@const examples = [{answer: "3.5", acceptable: ["3.5", "3.50", "7/2"], unacceptable: ["31/2", "3 1/2"]},
{answer: "2/3", acceptable: ["2/3", ".6666", ".6667", "0.666", "0.667"], unacceptable: ["0.66", ".66", "0.67", ".67"]},
{answer: "-1/3", acceptable: ["-1/3", "-.3333", "-0.333"], unacceptable: ["-.33", "-0.33"]}]}
<div class="spr-instruction-container flex flex-col gap-4 items-center">
    <div class="spr-instruction-text">
        <h1 class="spr-instruction-header text-xl font-bold">Student-produced response directions</h1>
        <ul class="spr-instruction-list list-disc ml-10 mt-2">
            <li>If you find <b>more than one correct answer</b>, enter only one answer.</li>
            <li>You can enter up to 5 characters for a <b>positive</b> answer and up to 6 characters (including the negative sign) for a <b>negative</b> answer.</li>
            <li>If your answer is a <b>fraction</b> that doesn't fit in the provided space, enter the decimal equivalent.</li>
            <li>If your answer is a <b>decimal</b> that doesn't fit in the provided space, enter it by truncating or rounding at the fourth digit.</li>
            <li>If your answer is a <b>mixed number</b> (such as 3$1\over2$), enter it as an improper fraction (7/2) or its decimal equivalent (3.5).</li>
            <li>Don't enter <b>symbols</b> such as a percent sign, comma, or dollar sign.</li>
        </ul>
    </div>
    <p>Examples</p>
    <table class="spr-table w-3/4 text-center [&_td]:border-solid [&_td]:border-black [&_td]:border-[1px] [&_td]:px-3 [&_td]:py-5">
        <tbody>
            <tr>
                <td>Answer</td>
                <td>Acceptable ways to enter answer</td>
                <td>Unacceptable: will NOT receive credit</td>
            </tr>
            {#each examples as example}
                <tr>
                    <td>{example.answer}</td>
                    <td>
                        <div class="flex flex-col gap-2 items-center">
                            {#each example.acceptable as acceptable}
                                <p class="font-['Chivo'] text-sm bg-[#ededed]">{acceptable}</p>
                            {/each}
                        </div>
                    </td>
                    <td class="spr-table-chivo">
                        <div class="flex flex-col gap-2 items-center">
                            {#each example.unacceptable as unacceptable}
                                <p class="font-['Chivo'] text-sm bg-[#ededed]">{unacceptable}</p>
                            {/each}
                        </div>
                    </td>
                </tr>
            {/each}
        </tbody>
    </table>
</div>
{/snippet}

{#snippet leftVerbal(questionData)}
    <div class="left-verbal-container max-w-[780px] m-auto flex flex-col gap-4">
        <!-- Figure -->
        {#if questionData.figure}
            <img class="m-auto" style:width={isMath ? "400px" : "90%"} src={questionData.figure} alt="Figure">
        {/if}

        <!-- Student's Notes Question | Fiction Question -->
        {#if questionData.intro}
            <div class="intro">{@html questionData.intro}</div>

            <!-- Student's Notes Question-->
            {#if Array.isArray(questionData.passage)}
                <ul class="ml-10 list-disc">
                    {#each questionData.passage as point}
                        <li>{@html point}</li>            
                    {/each}
                </ul>

            <!-- Fiction Question -->
            {:else}
                <p class="ml-8">{@html questionData.passage}</p>
            {/if}

        <!-- Paired Passage Question -->    
        {:else if questionData.passage2}
            <div class="font-bold text-black">Text 1</div>
            <p>{@html questionData.passage}</p>
            <div class="font-bold text-black mt-6">Text 2</div>
            <p>{@html questionData.passage2}</p>

        <!-- Single Passage Question -->
        {:else}
            <p>{@html questionData.passage}</p>
        {/if}
    </div>
{/snippet}

{#snippet question()}
    <div class="question-container max-w-[780px] flex flex-col gap-4 m-auto">
        <!-- Index, Mark, and Elimination Bar -->
        <UI.MarkBar {currentQuestionState} {setMarked} {isEliminateTool} {toggleElimination} {isSPR}/>

        <!-- Question -->
        <div class="question-text ml-2">
            {@html currentQuestion.question}
        </div>

        {#if isSPR}
            <UI.SPRAnswerBox {currentQuestionState} {setAnswer}/>
        {:else}
            <UI.MultipleChoices {currentQuestion} {currentQuestionState} {setAnswer} {setCrossed} {isEliminateTool}/>
        {/if}
    </div>
{/snippet}

<style>
    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>
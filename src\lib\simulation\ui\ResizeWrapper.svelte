<script lang="ts">
    const { left, right, limit, handleResize = (pos: number) => null, currentResizeState = 50 } = $props();

    let isResizing = $state(false);
    let offsetX = $state(0);
    let positionX  = $state(currentResizeState);
    let resizeBar: HTMLElement = $state(null);
    let leftSide: HTMLElement = $state(null);

    function startResizing(e: MouseEvent) {
        isResizing = true;
        offsetX = e.clientX - resizeBar.offsetLeft;
        document.body.style.userSelect = "none";
        document.body.style.cursor = "col-resize";
    }

    let windowWidth = $state(1000);
    let container: HTMLElement = $state(null);
</script>

<div class="w-full h-full flex relative" bind:this={container}>
    <div class="left overflow-y-auto" style:width={`${positionX}%`} bind:this={leftSide}>
        {@render left()}
    </div>
    <div class="right flex-1 ml-[5px] overflow-y-auto">
        {@render right()}
    </div>


    <!-- svelte-ignore a11y_no_static_element_interactions -->
    <div class="resize-bar w-[5px] h-full bg-[#D9D9D9] absolute cursor-col-resize" style:left={`${positionX}%`} onmousedown={startResizing} bind:this={resizeBar}>
        <!-- The draggable thumb/handle -->
        <button class="absolute flex h-9 w-5 items-center justify-center rounded-md bg-gray-800 text-white border-white border-[3px] border-solid left-1/2 -translate-x-1/2 top-[20%] !cursor-grab" class:!cursor-grabbing={isResizing}>
            <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
            >
                <!-- Left pointing solid triangle -->
                <path d="M10 6 L4 12 L10 18 Z"></path>
                <!-- Right pointing solid triangle -->
                <path d="M14 6 L20 12 L14 18 Z"></path>
            </svg>
        </button>
    </div>
</div>

<svelte:window bind:innerWidth={windowWidth} 
    onresize={() => {
        const rect = container.getBoundingClientRect();
        if (rect.width < limit * 2) {
            positionX = 50;
            return;
        }
        let x = Math.min(Math.max(positionX * rect.width / 100, limit), rect.width - limit);
        positionX = x / rect.width * 100;
        handleResize(positionX);
    }} 
    onmousemove={(e) => {
        if (!isResizing) return;
        const rect = container.getBoundingClientRect();
        if (rect.width < limit * 2) return;
        let x = e.clientX - rect.left - offsetX;
        x = Math.min(Math.max(x, limit), rect.width - limit);
        positionX = x / rect.width * 100;
        handleResize(positionX);
    }}
    onmouseup={() => {
        if (!isResizing) return;
        isResizing = false;
        document.body.style.userSelect = "";
        document.body.style.cursor = "";
    }}
/>


<style>
    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>
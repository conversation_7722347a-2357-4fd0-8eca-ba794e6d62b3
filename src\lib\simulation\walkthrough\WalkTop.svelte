<!-- 
    @component
    ## WalkTop
    The top section of the walkthrough page. Contains the title of the test and the timer.
-->

<script lang="ts">
	import { goto } from "$app/navigation";
    import { H2, H5, P2, But<PERSON> } from "$lib/ui";

    let {
        moduleTitle,
        isResultTab = $bindable(true)
    } = $props();
        
    let isSharePopUpOpen = $state(false);

    function setSharePopUp() {
        isSharePopUpOpen = !isSharePopUpOpen;
    }

    let isLinkCopied = $state(false);
    let shareURL = "https://dsat16.com/share-analysis/123";
    function copyToClipboard() {
        navigator.clipboard.writeText(shareURL)
        .then(() => {
            isLinkCopied = true;
        })
        .catch(err => {
            console.error('Failed to copy: ', err);
        });
    }

    let windowWidth = $state(1000);
</script>

<svelte:window bind:innerWidth={windowWidth} />

<!-- Top -->
<div class="top bg-white flex justify-between items-center border-b-[3px] border-[#505050] p-4 px-[64px] h-[90px] z-20 relative font-semibold font-['Inter']">
    <div class="title text-xl" style:font-family="Open Sans">{moduleTitle}</div>

    <div class="toggle-switch absolute left-1/2 -translate-x-1/2 flex w-[350px] h-14 border-black border-solid rounded-full border-[1.5px] overflow-hidden">
        <button class="w-1/2 toggle-button-left flex items-center justify-center gap-2 hover:bg-[#07cbf7]" class:bg-[var(--sky-blue)]={isResultTab} onclick={() => {isResultTab = true}}>
            <svg class="flex-shrink-0" class:hidden={!isResultTab} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18.7104 7.20986C18.6175 7.11613 18.5069 7.04174 18.385 6.99097C18.2632 6.9402 18.1324 6.91406 18.0004 6.91406C17.8684 6.91406 17.7377 6.9402 17.6159 6.99097C17.494 7.04174 17.3834 7.11613 17.2904 7.20986L9.84044 14.6699L6.71044 11.5299C6.61392 11.4366 6.49998 11.3633 6.37512 11.3141C6.25026 11.2649 6.11694 11.2408 5.98276 11.2431C5.84858 11.2454 5.71617 11.2741 5.59309 11.3276C5.47001 11.3811 5.35868 11.4583 5.26544 11.5549C5.1722 11.6514 5.09889 11.7653 5.04968 11.8902C5.00048 12.015 4.97635 12.1484 4.97867 12.2825C4.98099 12.4167 5.00972 12.5491 5.06321 12.6722C5.1167 12.7953 5.19392 12.9066 5.29044 12.9999L9.13044 16.8399C9.2234 16.9336 9.334 17.008 9.45586 17.0588C9.57772 17.1095 9.70843 17.1357 9.84044 17.1357C9.97245 17.1357 10.1032 17.1095 10.225 17.0588C10.3469 17.008 10.4575 16.9336 10.5504 16.8399L18.7104 8.67986C18.8119 8.58622 18.893 8.47257 18.9484 8.34607C19.0038 8.21957 19.0324 8.08296 19.0324 7.94486C19.0324 7.80676 19.0038 7.67015 18.9484 7.54365C18.893 7.41715 18.8119 7.3035 18.7104 7.20986Z" fill="black"/>
            </svg>
            <H5>Result</H5>
        </button>
        <div class="h-full w-[1.5px] bg-black"></div>
        <button class="w-1/2 toggle-button-right flex items-center justify-start pl-10 gap-2 hover:bg-[#07cbf7]" class:pl-5={!isResultTab} class:bg-[var(--sky-blue)]={!isResultTab} onclick={() => {isResultTab = false}}>
            <svg class="flex-shrink-0" class:hidden={isResultTab} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18.7104 7.20986C18.6175 7.11613 18.5069 7.04174 18.385 6.99097C18.2632 6.9402 18.1324 6.91406 18.0004 6.91406C17.8684 6.91406 17.7377 6.9402 17.6159 6.99097C17.494 7.04174 17.3834 7.11613 17.2904 7.20986L9.84044 14.6699L6.71044 11.5299C6.61392 11.4366 6.49998 11.3633 6.37512 11.3141C6.25026 11.2649 6.11694 11.2408 5.98276 11.2431C5.84858 11.2454 5.71617 11.2741 5.59309 11.3276C5.47001 11.3811 5.35868 11.4583 5.26544 11.5549C5.1722 11.6514 5.09889 11.7653 5.04968 11.8902C5.00048 12.015 4.97635 12.1484 4.97867 12.2825C4.98099 12.4167 5.00972 12.5491 5.06321 12.6722C5.1167 12.7953 5.19392 12.9066 5.29044 12.9999L9.13044 16.8399C9.2234 16.9336 9.334 17.008 9.45586 17.0588C9.57772 17.1095 9.70843 17.1357 9.84044 17.1357C9.97245 17.1357 10.1032 17.1095 10.225 17.0588C10.3469 17.008 10.4575 16.9336 10.5504 16.8399L18.7104 8.67986C18.8119 8.58622 18.893 8.47257 18.9484 8.34607C19.0038 8.21957 19.0324 8.08296 19.0324 7.94486C19.0324 7.80676 19.0038 7.67015 18.9484 7.54365C18.893 7.41715 18.8119 7.3035 18.7104 7.20986Z" fill="black"/>
            </svg>
            <H5>Analysis</H5>
        </button>
    </div>

    <div class="buttons-container flex gap-4 select-none items-center">
        <!-- {#if !isResultTab}
        <button class="ultility-button bg-[var(--sky-blue)]" aria-label="Send analysis to email">
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M28.5 6.5H7.5C6.30653 6.5 5.16193 6.97411 4.31802 7.81802C3.47411 8.66193 3 9.80653 3 11V26C3 27.1935 3.47411 28.3381 4.31802 29.182C5.16193 30.0259 6.30653 30.5 7.5 30.5H28.5C29.6935 30.5 30.8381 30.0259 31.682 29.182C32.5259 28.3381 33 27.1935 33 26V11C33 9.80653 32.5259 8.66193 31.682 7.81802C30.8381 6.97411 29.6935 6.5 28.5 6.5V6.5ZM27.885 9.5L19.065 18.32C18.9256 18.4606 18.7597 18.5722 18.5769 18.6483C18.3941 18.7245 18.198 18.7637 18 18.7637C17.802 18.7637 17.6059 18.7245 17.4231 18.6483C17.2403 18.5722 17.0744 18.4606 16.935 18.32L8.115 9.5H27.885ZM30 26C30 26.3978 29.842 26.7794 29.5607 27.0607C29.2794 27.342 28.8978 27.5 28.5 27.5H7.5C7.10218 27.5 6.72064 27.342 6.43934 27.0607C6.15804 26.7794 6 26.3978 6 26V11.615L14.82 20.435C15.6638 21.2777 16.8075 21.751 18 21.751C19.1925 21.751 20.3362 21.2777 21.18 20.435L30 11.615V26Z" fill="#333333"/>
            </svg>
        </button>

        <button class="ultility-button" onclick={setSharePopUp} aria-label="Share analysis">
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13.065 11.5648L16.5 8.11484V22.4998C16.5 22.8977 16.658 23.2792 16.9393 23.5605C17.2206 23.8418 17.6022 23.9998 18 23.9998C18.3978 23.9998 18.7794 23.8418 19.0607 23.5605C19.342 23.2792 19.5 22.8977 19.5 22.4998V8.11484L22.935 11.5648C23.0744 11.7054 23.2403 11.817 23.4231 11.8932C23.6059 11.9693 23.802 12.0085 24 12.0085C24.198 12.0085 24.3941 11.9693 24.5769 11.8932C24.7597 11.817 24.9256 11.7054 25.065 11.5648C25.2056 11.4254 25.3172 11.2595 25.3933 11.0767C25.4695 10.8939 25.5087 10.6979 25.5087 10.4998C25.5087 10.3018 25.4695 10.1058 25.3933 9.92298C25.3172 9.74019 25.2056 9.57429 25.065 9.43484L19.065 3.43484C18.9223 3.29828 18.7541 3.19124 18.57 3.11984C18.2048 2.96982 17.7952 2.96982 17.43 3.11984C17.2459 3.19124 17.0777 3.29828 16.935 3.43484L10.935 9.43484C10.7951 9.5747 10.6842 9.74074 10.6085 9.92347C10.5328 10.1062 10.4939 10.3021 10.4939 10.4998C10.4939 10.6976 10.5328 10.8935 10.6085 11.0762C10.6842 11.259 10.7951 11.425 10.935 11.5648C11.0749 11.7047 11.2409 11.8156 11.4236 11.8913C11.6064 11.967 11.8022 12.006 12 12.006C12.1978 12.006 12.3936 11.967 12.5764 11.8913C12.7591 11.8156 12.9251 11.7047 13.065 11.5648ZM31.5 20.9998C31.1022 20.9998 30.7206 21.1579 30.4393 21.4392C30.158 21.7205 30 22.102 30 22.4998V28.4998C30 28.8977 29.842 29.2792 29.5607 29.5605C29.2794 29.8418 28.8978 29.9998 28.5 29.9998H7.5C7.10218 29.9998 6.72064 29.8418 6.43934 29.5605C6.15804 29.2792 6 28.8977 6 28.4998V22.4998C6 22.102 5.84196 21.7205 5.56066 21.4392C5.27936 21.1579 4.89782 20.9998 4.5 20.9998C4.10218 20.9998 3.72064 21.1579 3.43934 21.4392C3.15804 21.7205 3 22.102 3 22.4998V28.4998C3 29.6933 3.47411 30.8379 4.31802 31.6818C5.16193 32.5257 6.30653 32.9998 7.5 32.9998H28.5C29.6935 32.9998 30.8381 32.5257 31.682 31.6818C32.5259 30.8379 33 29.6933 33 28.4998V22.4998C33 22.102 32.842 21.7205 32.5607 21.4392C32.2794 21.1579 31.8978 20.9998 31.5 20.9998Z" fill="black"/>
            </svg>    
        </button>
        {/if} -->

        <button class="nav-button bg-[#66E2FF] border-solid border-black py-3 px-6 rounded-full hover:bg-[#07cbf7] flex items-center gap-4" onclick={() => {goto('/study');}}>
            <!-- {#if windowWidth < 1200}
            <svg xmlns="http://www.w3.org/2000/svg" width="32px" height="32px" viewBox="0 0 21 21">
                <g fill="none" fill-rule="evenodd" stroke="#000000" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" transform="matrix(-1 0 0 1 18 3)">
                    <path d="m10.595 10.5 2.905-3-2.905-3"/>
                    <path d="m13.5 7.5h-9"/>
                    <path d="m10.5.5-8 .00224609c-1.1043501.00087167-1.9994384.89621131-2 2.00056153v9.99438478c.0005616 1.1043502.8956499 1.9996898 2 2.0005615l8 .0022461"/>
                </g>
            </svg>
            {:else} -->
            Back to Study Page
            <!-- {/if} -->
        </button>
    </div>
</div>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
{#if isSharePopUpOpen}
<div class="overlay fixed inset-0 bg-black/20 z-50 flex justify-center items-center" onclick={setSharePopUp}>
    <div class="share-pop-up bg-white p-16 relative flex flex-col items-center text-center gap-10 max-w-[620px] w-[90%] border-black border-solid border rounded-lg" onclick={(e) => e.stopPropagation()}>
        <button class="close-pop-up-button absolute top-4 right-4" onclick={setSharePopUp} aria-label="Close share analysis popup">
            <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="15" cy="15" r="15" fill="#66E2FF"/>
                <path d="M16.4 15L22.7 8.7C23.1 8.3 23.1 7.7 22.7 7.3C22.3 6.9 21.7 6.9 21.3 7.3L15 13.6L8.7 7.3C8.3 6.9 7.7 6.9 7.3 7.3C6.9 7.7 6.9 8.3 7.3 8.7L13.6 15L7.3 21.3C7.1 21.5 7 21.7 7 22C7 22.6 7.4 23 8 23C8.3 23 8.5 22.9 8.7 22.7L15 16.4L21.3 22.7C21.5 22.9 21.7 23 22 23C22.3 23 22.5 22.9 22.7 22.7C23.1 22.3 23.1 21.7 22.7 21.3L16.4 15Z" fill="white"/>
            </svg>
        </button>
    
        <H2>Share your Analysis</H2>
        <P2>Stay committed to your study plan with friends by sharing your test results and analytics.</P2>
        <div class="share-link-container flex gap-4 items-center w-full">
            <div class="flex gap-4 flex-1 link-display border border-solid border-[#777777] p-2 rounded-lg px-4 w-4/5">
                <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.1098 15.8901L8.22984 19.7701C7.76007 20.2235 7.1327 20.4768 6.47984 20.4768C5.82698 20.4768 5.1996 20.2235 4.72984 19.7701C4.49944 19.5406 4.31663 19.2679 4.19189 18.9676C4.06715 18.6673 4.00294 18.3453 4.00294 18.0201C4.00294 17.6949 4.06715 17.3729 4.19189 17.0726C4.31663 16.7723 4.49944 16.4996 4.72984 16.2701L8.60984 12.3901C8.79814 12.2018 8.90393 11.9464 8.90393 11.6801C8.90393 11.4138 8.79814 11.1584 8.60984 10.9701C8.42153 10.7818 8.16614 10.676 7.89984 10.676C7.63353 10.676 7.37814 10.7818 7.18984 10.9701L3.30984 14.8601C2.52819 15.7109 2.10546 16.8308 2.12991 17.9858C2.15436 19.1409 2.62412 20.2419 3.44107 21.0589C4.25802 21.8758 5.359 22.3456 6.51408 22.37C7.66917 22.3945 8.78904 21.9717 9.63984 21.1901L13.5298 17.3101C13.7181 17.1218 13.8239 16.8664 13.8239 16.6001C13.8239 16.3338 13.7181 16.0784 13.5298 15.8901C13.3415 15.7018 13.0861 15.596 12.8198 15.596C12.5535 15.596 12.2981 15.7018 12.1098 15.8901ZM20.6898 3.81009C19.8486 2.9741 18.7108 2.50488 17.5248 2.50488C16.3389 2.50488 15.2011 2.9741 14.3598 3.81009L10.4698 7.69009C10.3766 7.78333 10.3026 7.89402 10.2522 8.01585C10.2017 8.13767 10.1757 8.26824 10.1757 8.40009C10.1757 8.53195 10.2017 8.66252 10.2522 8.78434C10.3026 8.90617 10.3766 9.01686 10.4698 9.11009C10.5631 9.20333 10.6738 9.27729 10.7956 9.32775C10.9174 9.37821 11.048 9.40419 11.1798 9.40419C11.3117 9.40419 11.4423 9.37821 11.5641 9.32775C11.6859 9.27729 11.7966 9.20333 11.8898 9.11009L15.7698 5.23009C16.2396 4.77672 16.867 4.52335 17.5198 4.52335C18.1727 4.52335 18.8001 4.77672 19.2698 5.23009C19.5002 5.45958 19.683 5.7323 19.8078 6.03261C19.9325 6.33292 19.9967 6.65491 19.9967 6.98009C19.9967 7.30528 19.9325 7.62727 19.8078 7.92758C19.683 8.22789 19.5002 8.50061 19.2698 8.73009L15.3898 12.6101C15.2961 12.7031 15.2217 12.8137 15.1709 12.9355C15.1202 13.0574 15.094 13.1881 15.094 13.3201C15.094 13.4521 15.1202 13.5828 15.1709 13.7047C15.2217 13.8265 15.2961 13.9371 15.3898 14.0301C15.4828 14.1238 15.5934 14.1982 15.7153 14.249C15.8371 14.2998 15.9678 14.3259 16.0998 14.3259C16.2318 14.3259 16.3626 14.2998 16.4844 14.249C16.6063 14.1982 16.7169 14.1238 16.8098 14.0301L20.6898 10.1401C21.5258 9.29887 21.995 8.16107 21.995 6.97509C21.995 5.78912 21.5258 4.65131 20.6898 3.81009ZM8.82984 15.6701C8.92328 15.7628 9.03409 15.8361 9.15593 15.8859C9.27777 15.9356 9.40823 15.9609 9.53984 15.9601C9.67144 15.9609 9.80191 15.9356 9.92374 15.8859C10.0456 15.8361 10.1564 15.7628 10.2498 15.6701L15.1698 10.7501C15.3581 10.5618 15.4639 10.3064 15.4639 10.0401C15.4639 9.77379 15.3581 9.5184 15.1698 9.33009C14.9815 9.14179 14.7261 9.036 14.4598 9.036C14.1935 9.036 13.9381 9.14179 13.7498 9.33009L8.82984 14.2501C8.73611 14.3431 8.66171 14.4537 8.61095 14.5755C8.56018 14.6974 8.53404 14.8281 8.53404 14.9601C8.53404 15.0921 8.56018 15.2228 8.61095 15.3447C8.66171 15.4665 8.73611 15.5771 8.82984 15.6701Z" fill="#777777"/>
                </svg>    
                <P2 --text-color="#777777" isEllipsis>{shareURL}</P2>
            </div>
            <Button onclick={copyToClipboard}>
                {isLinkCopied ? "Copied!" : "Copy"}
            </Button>
        </div>
    </div>
</div>
{/if}



<style>
    .ultility-button {
        border: 3px solid var(--charcoal);
        border-radius: 50%;
        padding: 0.25rem;
        box-shadow: 0.25rem 0.25rem ;
    }

    .ultility-button:active {
        box-shadow: none;
        translate: 0.25rem 0.25rem
    }

    .share-pop-up {
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }
</style>
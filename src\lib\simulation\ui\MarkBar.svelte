<script lang="ts">
    let {
        currentQuestionState,
        setMarked = null,
        toggleElimination = null,
        isEliminateTool = false,
        isSPR = false,
        isWalkthrough = false
    } = $props();
</script>
<!-- Index, Mark, and Elimination Bar -->
<div class="mark-bar font-['Inter'] w-full flex-shrink-0 bg-[#e6e6e6] select-none flex flex-col">
    <div class="flex items-center w-full flex-1 flex gap-2">
        <span class="number w-8 h-8 bg-black flex-shrink-0 text-white font-semibold flex items-center justify-center text-xl">{currentQuestionState.index + 1}</span>
        <button class="mark flex items-center gap-1" class:!cursor-default={isWalkthrough} class:opacity-50={isWalkthrough} onclick={setMarked}>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
                {#if !currentQuestionState.marked}
                    <path d="M16.6668 2.08301H8.3335C7.5047 2.08301 6.70985 2.41225 6.1238 2.9983C5.53774 3.58435 5.2085 4.37921 5.2085 5.20801V21.8747C5.20778 22.0582 5.25556 22.2387 5.34702 22.3979C5.43848 22.557 5.57037 22.6891 5.72934 22.7809C5.88769 22.8724 6.06732 22.9205 6.25017 22.9205C6.43302 22.9205 6.61265 22.8724 6.771 22.7809L12.5002 19.4684L18.2293 22.7809C18.3881 22.8709 18.5677 22.9176 18.7502 22.9163C18.9326 22.9176 19.1123 22.8709 19.271 22.7809C19.43 22.6891 19.5619 22.557 19.6533 22.3979C19.7448 22.2387 19.7926 22.0582 19.7918 21.8747V5.20801C19.7918 4.37921 19.4626 3.58435 18.8765 2.9983C18.2905 2.41225 17.4956 2.08301 16.6668 2.08301ZM17.7085 20.0726L13.021 17.3643C12.8627 17.2728 12.683 17.2247 12.5002 17.2247C12.3173 17.2247 12.1377 17.2728 11.9793 17.3643L7.29184 20.0726V5.20801C7.29184 4.93174 7.40158 4.66679 7.59694 4.47144C7.79229 4.27609 8.05724 4.16634 8.3335 4.16634H16.6668C16.9431 4.16634 17.2081 4.27609 17.4034 4.47144C17.5988 4.66679 17.7085 4.93174 17.7085 5.20801V20.0726Z" fill="black"/>
                {:else}
                    <path d="M16.6668 2.08301H8.3335C6.56266 2.08301 5.2085 3.43717 5.2085 5.20801V21.8747C5.2085 22.083 5.2085 22.1872 5.31266 22.3955C5.62516 22.9163 6.25016 23.0205 6.771 22.8122L12.5002 19.4788L18.2293 22.8122C18.4377 22.9163 18.5418 22.9163 18.7502 22.9163C19.3752 22.9163 19.7918 22.4997 19.7918 21.8747V5.20801C19.7918 3.43717 18.4377 2.08301 16.6668 2.08301Z" fill="#FF66C4"/>
                {/if}
            </svg>
            <div class="mark-text text-black text-[15px]">{currentQuestionState.marked ? "Unmark" : "Mark for Review"}</div>
        </button>
        
        {#if !isWalkthrough && !isSPR}
            <button class="toggle-elimination w-[30px] h-[28px] flex-shrink-0 ml-auto mr-2" onclick={toggleElimination}>
                {#if isEliminateTool}
                    <svg xmlns="http://www.w3.org/2000/svg" width="31" height="30" viewBox="0 0 31 30" fill="none">
                        <g clip-path="url(#clip0_904_392)">
                        <path d="M25 1H6C2.96243 1 0.5 3.46243 0.5 6.5V23.5C0.5 26.5376 2.96243 29 6 29H25C28.0376 29 30.5 26.5376 30.5 23.5V6.5C30.5 3.46243 28.0376 1 25 1Z" fill="var(--sky-blue)" stroke="black"/>
                        <path d="M6.82812 19.0834H5L8.32848 9.62891H10.4428L13.7759 19.0834H11.9478L9.42259 11.5678H9.34872L6.82812 19.0834ZM6.88814 15.3764H11.8739V16.7521H6.88814V15.3764Z" fill="white"/>
                        <path d="M12.5508 19.0834V9.62891H16.1701C16.8534 9.62891 17.4212 9.73663 17.8736 9.95206C18.3291 10.1644 18.6692 10.4553 18.8939 10.8245C19.1216 11.1938 19.2355 11.6124 19.2355 12.0802C19.2355 12.4649 19.1616 12.7942 19.0139 13.0681C18.8662 13.339 18.6677 13.559 18.4184 13.7283C18.1691 13.8976 17.8905 14.0191 17.5828 14.093V14.1853C17.9182 14.2038 18.2399 14.3069 18.5476 14.4946C18.8585 14.6793 19.1124 14.9409 19.3093 15.2794C19.5063 15.618 19.6048 16.0273 19.6048 16.5074C19.6048 16.9968 19.4863 17.4369 19.2493 17.8277C19.0123 18.2155 18.6553 18.5217 18.1783 18.7464C17.7013 18.9711 17.1011 19.0834 16.3779 19.0834H12.5508ZM14.2635 17.6523H16.1055C16.7272 17.6523 17.175 17.5338 17.4489 17.2968C17.7259 17.0568 17.8644 16.749 17.8644 16.3735C17.8644 16.0935 17.7951 15.8411 17.6566 15.6164C17.5181 15.3887 17.3212 15.2102 17.0657 15.0809C16.8103 14.9486 16.5056 14.8824 16.1517 14.8824H14.2635V17.6523ZM14.2635 13.6498H15.9578C16.2532 13.6498 16.5195 13.596 16.7564 13.4882C16.9934 13.3774 17.1796 13.222 17.315 13.022C17.4535 12.8188 17.5228 12.5788 17.5228 12.3018C17.5228 11.9356 17.3935 11.634 17.135 11.397C16.8795 11.16 16.4994 11.0415 15.9947 11.0415H14.2635V13.6498Z" fill="white"/>
                        <path d="M26.7175 12.8192H24.991C24.9417 12.5361 24.851 12.2853 24.7186 12.0668C24.5863 11.8452 24.4216 11.6574 24.2247 11.5036C24.0277 11.3497 23.803 11.2343 23.5506 11.1573C23.3014 11.0773 23.0321 11.0373 22.7428 11.0373C22.2288 11.0373 21.7733 11.1665 21.3763 11.4251C20.9793 11.6805 20.6684 12.056 20.4438 12.5515C20.2191 13.0439 20.1068 13.6456 20.1068 14.3565C20.1068 15.0798 20.2191 15.6892 20.4438 16.1847C20.6715 16.6771 20.9823 17.0495 21.3763 17.3018C21.7733 17.5511 22.2273 17.6758 22.7381 17.6758C23.0213 17.6758 23.286 17.6388 23.5322 17.565C23.7815 17.488 24.0046 17.3757 24.2016 17.228C24.4016 17.0803 24.5693 16.8987 24.7048 16.6832C24.8433 16.4678 24.9387 16.2216 24.991 15.9446L26.7175 15.9538C26.6529 16.4032 26.5129 16.8248 26.2974 17.2188C26.0851 17.6127 25.8066 17.9605 25.4619 18.2621C25.1172 18.5606 24.714 18.7945 24.2523 18.9638C23.7907 19.13 23.2783 19.2131 22.7151 19.2131C21.8841 19.2131 21.1424 19.0207 20.4899 18.636C19.8375 18.2513 19.3235 17.6958 18.948 16.9695C18.5725 16.2431 18.3848 15.3722 18.3848 14.3565C18.3848 13.3378 18.5741 12.4669 18.9526 11.7436C19.3312 11.0173 19.8467 10.4618 20.4992 10.0771C21.1516 9.69235 21.8903 9.5 22.7151 9.5C23.2413 9.5 23.7307 9.57386 24.1831 9.72159C24.6355 9.86932 25.0387 10.0863 25.3926 10.3725C25.7465 10.6557 26.0374 11.0034 26.2651 11.4158C26.496 11.8252 26.6468 12.293 26.7175 12.8192Z" fill="white"/>
                        <g filter="url(#filter0_d_904_392)">
                            <path d="M27.7818 5.26331L27.1357 4.5L4.99965 23.2348L5.64567 23.9981L27.7818 5.26331Z" fill="white"/>
                        </g>
                        </g>
                        <defs>
                        <filter id="filter0_d_904_392" x="5" y="4.5" width="22.7822" height="20.498" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="1"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.4 0 0 0 0 0.886275 0 0 0 0 1 0 0 0 1 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_904_392"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_904_392" result="shape"/>
                        </filter>
                        <clipPath id="clip0_904_392">
                            <rect width="31" height="29" fill="white" transform="translate(0 0.5)"/>
                        </clipPath>
                        </defs>
                    </svg>
                {:else}
                    <svg width="31" height="30" viewBox="0 0 31 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_1450_1108)">
                            <path d="M25 1H6C2.96243 1 0.5 3.46243 0.5 6.5V23.5C0.5 26.5376 2.96243 29 6 29H25C28.0376 29 30.5 26.5376 30.5 23.5V6.5C30.5 3.46243 28.0376 1 25 1Z" fill="white" stroke="black"/>
                            <path d="M6.82812 19.0839H5L8.32848 9.62939H10.4428L13.7759 19.0839H11.9478L9.42259 11.5683H9.34872L6.82812 19.0839ZM6.88814 15.3769H11.8739V16.7526H6.88814V15.3769Z" fill="black"/>
                            <path d="M12.5508 19.0839V9.62939H16.1701C16.8534 9.62939 17.4212 9.73711 17.8736 9.95254C18.3291 10.1649 18.6692 10.4557 18.8939 10.825C19.1216 11.1943 19.2355 11.6129 19.2355 12.0807C19.2355 12.4654 19.1616 12.7947 19.0139 13.0686C18.8662 13.3395 18.6677 13.5595 18.4184 13.7288C18.1691 13.8981 17.8905 14.0196 17.5828 14.0935V14.1858C17.9182 14.2043 18.2399 14.3074 18.5476 14.4951C18.8585 14.6798 19.1124 14.9414 19.3093 15.2799C19.5063 15.6185 19.6048 16.0278 19.6048 16.5079C19.6048 16.9973 19.4863 17.4374 19.2493 17.8282C19.0123 18.216 18.6553 18.5222 18.1783 18.7469C17.7013 18.9716 17.1011 19.0839 16.3779 19.0839H12.5508ZM14.2635 17.6528H16.1055C16.7272 17.6528 17.175 17.5343 17.4489 17.2973C17.7259 17.0573 17.8644 16.7495 17.8644 16.374C17.8644 16.094 17.7951 15.8416 17.6566 15.6169C17.5181 15.3892 17.3212 15.2107 17.0657 15.0814C16.8103 14.9491 16.5056 14.8829 16.1517 14.8829H14.2635V17.6528ZM14.2635 13.6503H15.9578C16.2532 13.6503 16.5195 13.5965 16.7564 13.4887C16.9934 13.3779 17.1796 13.2225 17.315 13.0225C17.4535 12.8193 17.5228 12.5793 17.5228 12.3023C17.5228 11.9361 17.3935 11.6345 17.135 11.3975C16.8795 11.1605 16.4994 11.042 15.9947 11.042H14.2635V13.6503Z" fill="black"/>
                            <path d="M26.7175 12.8192H24.991C24.9417 12.5361 24.851 12.2853 24.7186 12.0668C24.5863 11.8452 24.4216 11.6574 24.2247 11.5036C24.0277 11.3497 23.803 11.2343 23.5506 11.1573C23.3014 11.0773 23.0321 11.0373 22.7428 11.0373C22.2288 11.0373 21.7733 11.1665 21.3763 11.4251C20.9793 11.6805 20.6684 12.056 20.4438 12.5515C20.2191 13.0439 20.1068 13.6456 20.1068 14.3565C20.1068 15.0798 20.2191 15.6892 20.4438 16.1847C20.6715 16.6771 20.9823 17.0495 21.3763 17.3018C21.7733 17.5511 22.2273 17.6758 22.7381 17.6758C23.0213 17.6758 23.286 17.6388 23.5322 17.565C23.7815 17.488 24.0046 17.3757 24.2016 17.228C24.4016 17.0803 24.5693 16.8987 24.7048 16.6832C24.8433 16.4678 24.9387 16.2216 24.991 15.9446L26.7175 15.9538C26.6529 16.4032 26.5129 16.8248 26.2974 17.2188C26.0851 17.6127 25.8066 17.9605 25.4619 18.2621C25.1172 18.5606 24.714 18.7945 24.2523 18.9638C23.7907 19.13 23.2783 19.2131 22.7151 19.2131C21.8841 19.2131 21.1424 19.0207 20.4899 18.636C19.8375 18.2513 19.3235 17.6958 18.948 16.9695C18.5725 16.2431 18.3848 15.3722 18.3848 14.3565C18.3848 13.3378 18.5741 12.4669 18.9526 11.7436C19.3312 11.0173 19.8467 10.4618 20.4992 10.0771C21.1516 9.69235 21.8903 9.5 22.7151 9.5C23.2413 9.5 23.7307 9.57386 24.1831 9.72159C24.6355 9.86932 25.0387 10.0863 25.3926 10.3725C25.7465 10.6557 26.0374 11.0034 26.2651 11.4158C26.496 11.8252 26.6468 12.293 26.7175 12.8192Z" fill="black"/>
                            <g filter="url(#filter0_d_1450_1108)">
                                <path d="M27.7827 5.26331L27.1367 4.5L5.00062 23.2348L5.64665 23.9981L27.7827 5.26331Z" fill="black"/>
                            </g>
                        </g>
                        <defs>
                            <filter id="filter0_d_1450_1108" x="5" y="4.5" width="22.7832" height="20.498" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="1"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1450_1108"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1450_1108" result="shape"/>
                            </filter>
                                <clipPath id="clip0_1450_1108">
                                <rect width="31" height="29" fill="white" transform="translate(0 0.5)"/>
                                </clipPath>
                        </defs>
                    </svg>
                {/if}
            </button>
        {/if}
    </div>
    <div class="h-[3px] w-full bg-black"></div>
</div>
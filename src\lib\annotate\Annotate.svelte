<script lang="ts">
    import { Annotate, colors } from "./annotateManager";
	import { untrack } from "svelte";

    let { children, key = "default" } = $props();

    // Annotate Content
    let childrenContainer: HTMLElement = $state(null);

    const annotateList = {};

    let currentAnnotate = $state(null);

    $effect(() => {
        if (!(key in annotateList)) {
            annotateList[key] = new Annotate();
        }

        untrack(() => {
            currentAnnotate = annotateList[key];
            currentAnnotate.setRoot(childrenContainer);
        });
    })

    // Annotate Tool Bar

    let range: Range = $state(null);
    let annoTool: HTMLElement = $state(null);

    let isNewSelection = $state(true);

    function handleClick(e: MouseEvent) {
        const selection = document.getSelection();
        if (!selection || !selection.rangeCount || selection.isCollapsed) {
            range = currentAnnotate.getRange(currentAnnotate.selectedHighlight);
            isNewSelection = false;
            return;
        }

        const tempRange = selection.getRangeAt(0);

        if (!childrenContainer.contains(tempRange.commonAncestorContainer)) return;

        range = tempRange;
        isNewSelection = true;
    }

    function handleDeleteHighlight() {
        currentAnnotate.deleteHighlight();
        range = null;
    }

    function handleHighlight(color: string) {
        currentColor = color;
        if (isNewSelection) {
            currentAnnotate.highlight(range, color);
            isNewSelection = false;
        }
        else currentAnnotate.changeHighlightColor(color);
    }

    let isOverflowRight: boolean = $state(false);
    

    $effect(() => {
        if (range) {
            annoTool.style.display = 'flex';

            const rect = range.getBoundingClientRect();
            const annoToolLeft = Math.min(window.innerWidth - annoTool.offsetWidth, Math.max(0, rect.left + rect.width / 2 - annoTool.offsetWidth / 2));
            annoTool.style.left = `${annoToolLeft}px`;
            annoTool.style.top = `${rect.top - annoTool.offsetHeight < 6 ? rect.top + rect.height + 3 : rect.top - annoTool.offsetHeight - 3}px`;

            isOverflowRight = annoToolLeft + annoTool.offsetWidth * 2.05 > window.innerWidth

            untrack(() => {
                noteTextValue = currentAnnotate.getNoteText(currentAnnotate.selectedHighlight);
                isNoteOpen = !!noteTextValue;
                if (!isNewSelection) currentColor = currentAnnotate.getHighlightColor();
            });
        } else {
            window.getSelection().removeAllRanges();
            currentAnnotate.setFocus(null);
            annoTool.style.display = 'none';
        }
    })

    let noteTextValue = $state('');
    let noteSelectedText = $derived(truncateText(range?.toString() ?? '', 22));
    let isNoteOpen = $state(false);
    let noteTextArea: HTMLTextAreaElement = $state(null);

    function handleAddNote(e: MouseEvent) {
        if (!isNoteOpen) {
            if (isNewSelection) {
                currentAnnotate.highlight(range, currentColor);
                isNewSelection = false;
            }
            isNoteOpen = true;
        }
    }

    function handleDeleteNote() {
        isNoteOpen = false;
        noteTextValue = '';
    }

    // $effect(() => {
    //     if (isNoteOpen) {
    //         noteTextArea.focus();
    //     }
    // });

    // $effect(() => {
    //     currentAnnotate.setNoteText(noteTextValue);
    // })

    let currentColor = $state('yellow');


    function truncateText(text: string, limit: number): string {
        if (text === null) return '';
		return text.length >= limit ? text.slice(0, limit - 3) + ' ...' : text;
	}

    let hoveredRange = $state(null);
    let hoveredTextContainer: HTMLDivElement = $state(null);
    let hoveredTextValue = $state('')

    function handleMouseOver(e: MouseEvent) {
        const mark = (e.target as HTMLElement).closest('mark');
        if (mark) {
            hoveredRange = currentAnnotate.getRange(currentAnnotate.hoveredHighlight.pos);
        }
    }

    function handleMouseOut(e: MouseEvent) {
        const mark = (e.target as HTMLElement).closest('mark');
        if (mark) {
            hoveredRange = null;
        }
    }

    // $effect(() => {
    //     if (hoveredRange && !range) {
    //         hoveredTextValue = currentAnnotate.getNoteText(currentAnnotate.hoveredHighlight);
    //         if (!hoveredTextValue) return;
    //         hoveredTextContainer.style.display = 'block';
    //         const rect = hoveredRange.getBoundingClientRect();
    //         hoveredTextContainer.style.left = `${Math.min(window.innerWidth - hoveredTextContainer.offsetWidth, Math.max(0, rect.left + rect.width / 2 - hoveredTextContainer.offsetWidth / 2))}px`;
    //         hoveredTextContainer.style.top = `${rect.top - hoveredTextContainer.offsetHeight < 6 ? rect.top + rect.height + 3 : rect.top - hoveredTextContainer.offsetHeight - 3}px`;
    //     } else {
    //         hoveredTextContainer.style.display = 'none';
    //     }
    // })
</script>

<svelte:window onmousedown={() => range = null} onclick={handleClick} onmouseover={handleMouseOver} onmouseout={handleMouseOut}/>


{#key key}
<div class="children-container" bind:this={childrenContainer}>
    {@render children?.()}
</div>
{/key}

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div class="absolute hidden" bind:this={annoTool} onmousedown={(e) => e.stopPropagation()} onclick={(e) => e.stopPropagation()}> 
    <div class="relative">
        <div class="h-10 buttons-container border border-black rounded-full bg-white px-2 py-1 flex gap-2 items-center [&_button]:rounded-full [&_button]:w-8 [&_button]:h-8 [&_button]:border [&_button]:border-solid [&_button]:bg-white [&_button]:flex [&_button]:justify-center [&_button]:items-center [&_button:hover]:border-[#1c1c1c] [&_button:hover]:border-2" onmousedown={(e) => e.preventDefault()}>
            {#each Object.keys(colors) as color}
                <button class="hover:border-[#1c1c1c] hover:border-2" style:background-color={colors[color].normal} onmouseenter={(e) => e.currentTarget.style.backgroundColor = colors[color].focus} onmouseleave={(e) => e.currentTarget.style.backgroundColor = colors[color].normal} onclick={() => handleHighlight(color)} aria-label="Highlight color"></button>
            {/each}
            <button class="delete-button hover:bg-[lightgray]" aria-label="Delete highlight" onclick={handleDeleteHighlight}>
                <svg width="85%" height="85%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 3H15M3 6H21M19 6L18.2987 16.5193C18.1935 18.0975 18.1409 18.8867 17.8 19.485C17.4999 20.0118 17.0472 20.4353 16.5017 20.6997C15.882 21 15.0911 21 13.5093 21H10.4907C8.90891 21 8.11803 21 7.49834 20.6997C6.95276 20.4353 6.50009 20.0118 6.19998 19.485C5.85911 18.8867 5.8065 18.0975 5.70129 16.5193L5 6M10 10.5V15.5M14 10.5V15.5" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>

            <div class="separate-line w-[1px] h-[70%] bg-[lightgray]"></div>

            <button class="add-note-button hover:bg-[lightgray]" aria-label="Add note" onclick={handleAddNote}>
                <svg xmlns="http://www.w3.org/2000/svg" width="85%" height="85%" viewBox="0 0 24 24" fill="none">
                    <path d="M10 12H14M12 10V14M19.9592 15H16.6C16.0399 15 15.7599 15 15.546 15.109C15.3578 15.2049 15.2049 15.3578 15.109 15.546C15 15.7599 15 16.0399 15 16.6V19.9592M20 14.1031V7.2C20 6.07989 20 5.51984 19.782 5.09202C19.5903 4.71569 19.2843 4.40973 18.908 4.21799C18.4802 4 17.9201 4 16.8 4H7.2C6.0799 4 5.51984 4 5.09202 4.21799C4.71569 4.40973 4.40973 4.71569 4.21799 5.09202C4 5.51984 4 6.0799 4 7.2V16.8C4 17.9201 4 18.4802 4.21799 18.908C4.40973 19.2843 4.71569 19.5903 5.09202 19.782C5.51984 20 6.0799 20 7.2 20H14.1031C14.5923 20 14.8369 20 15.067 19.9447C15.2711 19.8957 15.4662 19.8149 15.6451 19.7053C15.847 19.5816 16.0199 19.4086 16.3658 19.0627L19.0627 16.3658C19.4086 16.0199 19.5816 15.847 19.7053 15.6451C19.8149 15.4662 19.8957 15.2711 19.9447 15.067C20 14.8369 20 14.5923 20 14.1031Z" stroke="#000000" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" fill={colors[currentColor].normal}/>
                </svg>
            </button>
        </div>
        <div class="note-container w-full bg-white border border-solid border-black rounded-lg absolute top-0 overflow-hidden flex flex-col -translate-y-[1px]" style:left={!isOverflowRight ? "105%" : "-105%"} class:hidden={!isNoteOpen}>
            <div class="note-head p-2 font-semibold flex justify-between" style:background-color={colors[currentColor].normal}>
                <div class="note-head-title">{noteSelectedText}</div>
                <button class="bg-white hover:bg-[lightgray] rounded-full h-6 w-6 flex justify-center items-center flex-shrink-0" onclick={handleDeleteNote} aria-label="Delete note">
                    <svg width="85%" height="85%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 3H15M3 6H21M19 6L18.2987 16.5193C18.1935 18.0975 18.1409 18.8867 17.8 19.485C17.4999 20.0118 17.0472 20.4353 16.5017 20.6997C15.882 21 15.0911 21 13.5093 21H10.4907C8.90891 21 8.11803 21 7.49834 20.6997C6.95276 20.4353 6.50009 20.0118 6.19998 19.485C5.85911 18.8867 5.8065 18.0975 5.70129 16.5193L5 6M10 10.5V15.5M14 10.5V15.5" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <textarea class="note-textarea p-2 w-full resize-none focus:outline-none resize-none" 
            oninput={(e) => {
                const el = e.currentTarget;
                el.style.height = "auto";
                el.style.height = (Math.min(el.scrollHeight, 88)) + "px";
            }} bind:value={noteTextValue} bind:this={noteTextArea}></textarea>
        </div>
    </div>
</div>

<div class="border border-black border-2 rounded-lg px-1 min-w-20 font-semibold absolute hidden bg-white z-50" bind:this={hoveredTextContainer}>{truncateText(hoveredTextValue, 25)}</div>

<style>
    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: white;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #9e9e9e;
        border-radius: 5px;
    }
</style>
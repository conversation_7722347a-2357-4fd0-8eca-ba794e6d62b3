<!-- svelte-ignore state_referenced_locally -->
<script lang='ts'>
    import { Top, Bottom, Middle, Review } from '$lib/simulation/walkthrough';
    import AnalysisContainer from '$lib/simulation/analysis/AnalysisContainer.svelte';
	import { P1, H2 } from "$lib/ui";

    interface StudentAnswer {
        index: number;
        answer: number | string | null;
        isCorrect: boolean;
    };

    let { modules, studentData, simulationId } = $props();
    let { skillScores, studentLastAttempt, studentCurrentAttempt } = studentData;

    studentData = studentCurrentAttempt;

    let currentQuestionIndex = $state(27);
    let currentModuleIndex = $state(0);

    let currentModule = $derived(modules[currentModuleIndex]);

    let studentAllAnswers: StudentAnswer[][] = Object.values(studentData.answers);

    studentAllAnswers.forEach((module) => {
        module.forEach((question, index) => question.index = index);
    });

    let currentModuleAnswers = $derived(studentAllAnswers[currentModuleIndex]);

    let currentQuestion = $derived(currentModule.questions[currentQuestionIndex]);
    let currentQuestionAnswer = $derived(currentModuleAnswers[currentQuestionIndex]);

    let moduleTitle = $derived(currentModule.title);

    let isMath = $derived(['Algebra', 'Data Analysis', 'Geometry'].includes(currentModule?.questions[currentQuestionIndex]?.topic));

    function setQuestion(index: number) {
        if (index < 0) {
            currentModuleIndex--;
            currentQuestionIndex = currentModule.questions.length;
        } else if (index > currentModule.questions.length) {
            currentModuleIndex++;
            currentQuestionIndex = 0;
        } else {
            currentQuestionIndex = index;
        }
    }

    function toReview() {
        currentQuestionIndex = currentModule.questions.length;
    }

    let isReview = $derived(currentQuestionIndex === currentModule.questions.length);

    function setModule(index: number) {
        currentModuleIndex = index;
        currentQuestionIndex = [27, 27, 22, 22][index];
    }

    // Window properties
    let windowWidth = $state(1000);

    let isResultTab = $state(true);
</script>

<svelte:head>
    <title>Simulation {simulationId} - Analysis</title>
</svelte:head>

<svelte:window bind:innerWidth={windowWidth} oncontextmenu={(e) => e.preventDefault()}/>


<div class="simulation-container fixed inset-0 flex flex-col">
    <div class="top-container h-[90px]">
        <Top {moduleTitle} bind:isResultTab/>
    </div>

    {#if isResultTab}
    <div class="middle-container flex-1 overflow-y-auto flex flex-col items-center">
        {#if !isReview}
            <Middle {currentQuestion} {currentQuestionAnswer} {isMath} />
        {:else}
            <Review {moduleTitle} {currentModuleAnswers} {setQuestion} {setModule} {currentModuleIndex} {studentData}/>
        {/if}
    </div>

    <div class="bottom-container h-[90px]">
        <Bottom {moduleTitle} {currentQuestionIndex} {currentModuleAnswers}  {setQuestion} {toReview} {isReview} {currentModuleIndex}/>
    </div>
    {:else}
    <div class="flex-1 overflow-y-auto flex flex-col items-center">
        <AnalysisContainer {studentData} skillScores={skillScores} studentLastAttempt={studentLastAttempt}/>
    </div>
    {/if}
</div>



{#if windowWidth < 920}
<div class="fixed inset-0 flex justify-center items-center bg-white z-[100]">
	<div class="w-3/4 max-w-[600px] flex flex-col items-center justify-center gap-4 text-center">
		<H2>Caution</H2>
		<P1>
			This feature is not supported on this device. To access <span class="font-bold">DSAT16 Analysis</span>, please switch to a device with a larger screen.
        </P1>
	</div>
</div>
{/if}


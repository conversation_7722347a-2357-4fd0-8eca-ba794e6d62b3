<!-- 
    @component
    ## WalkMid
    The middle section of the walkthrough page. Can be either a math question or a reading question.
-->

<script module lang="ts">
    declare global {
        interface Window {
            MathJax: any;
        }
    }
</script>

<script lang='ts'>
	import * as UI from "../ui";

    let {
        currentQuestion,
        currentQuestionAnswer,
        isMath,
    } = $props();

    let isSPR = $derived(!(currentQuestion.answerChoices));

    // Resize function
    let isResizing = $state(false);
    let offsetX = $state(0);
    let positionX  = $state("50%");
    let resizeBar: HTMLElement = $state();
    let leftSide: HTMLElement = $state();
    let windowWidth = $state(1000);

    function startResizing(e: MouseEvent) {
        isResizing = true;
        offsetX = e.clientX - resizeBar.offsetLeft;
        document.body.style.userSelect = "none";
        document.body.style.cursor = "col-resize";
    }

    let middleSection: HTMLElement = $state();


    // Render Latex again each time changing question
    $effect(() => {
        if (middleSection) {
            window.MathJax = {
                tex: {
                    inlineMath: [["$", "$"], ["\\(", "\\)"]],
                    displayMath: [["$$", "$$"], ["\\[", "\\]"]]
                },
                svg: { fontCache: "global" }
            };

            let script = document.createElement('script');
            script.src = "https://cdn.jsdelivr.net/npm/mathjax@4/tex-chtml.js";
            document.head.append(script);
        }
    });
</script>

<svelte:window bind:innerWidth={windowWidth} onresize={() => {
    if (leftSide.offsetWidth <= 460) positionX = `${((460 / windowWidth) * 100)}%`;
    if (windowWidth - leftSide.offsetWidth <= 460) positionX = `${((windowWidth - 460) / windowWidth) * 100}%`;
}} />

<svelte:document 
    onmousemove={(e) => {
        if (!isResizing) return;
        if (e.clientX < 460 || e.clientX > window.innerWidth - 460) return;
        positionX = `${((e.clientX - offsetX) / window.innerWidth) * 100}%`;
    }}
    onmouseup={() => {
        if (!isResizing) return;
        isResizing = false;
        document.body.style.userSelect = "";
        document.body.style.cursor = "";
    }}
/>

<!-- svelte-ignore a11y_no_static_element_interactions -->
{#key currentQuestion}
<div class="middle flex w-full h-full relative gap-[5px] justify-center leading-relaxed font-['Merriweather'] text-[#333]" bind:this={middleSection}>
    {#if isMath}
        <div class="left p-10 overflow-y-auto" style:width={positionX} bind:this={leftSide}>
            {@render question()}
        </div>

        <div class="right flex-1 p-10 overflow-y-auto">
            <div class="font-semibold text-lg mb-2">Solution:</div>
            <p>{@html currentQuestion.explanation}</p>
        </div>
    {:else}
        <div class="left p-10 overflow-y-auto" style:width={positionX} bind:this={leftSide}>
            {@render leftVerbal()}
            <div class="font-semibold text-lg mt-6 mb-2">Explanation:</div>
            <p>{@html currentQuestion.explanation}</p>
        </div>

        <div class="right flex-1 p-10 overflow-y-auto">
            {@render question()}
        </div>
    {/if}

    <div class="resize-bar w-[5px] h-full bg-[#D9D9D9] absolute cursor-col-resize" style:left={positionX} onmousedown={startResizing} bind:this={resizeBar}>
        <!-- The draggable thumb/handle -->
        <button class="absolute flex h-9 w-5 items-center justify-center rounded-md bg-gray-800 text-white border-white border-[3px] border-solid left-1/2 -translate-x-1/2 top-[20%] !cursor-grab active:!cursor-grabbing" >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
            >
                <!-- Left pointing solid triangle -->
                <path d="M10 6 L4 12 L10 18 Z"></path>
                <!-- Right pointing solid triangle -->
                <path d="M14 6 L20 12 L14 18 Z"></path>
            </svg>
        </button>
    </div>
</div>
{/key}


{#snippet leftVerbal()}
    <div class="left-verbal-container max-w-[780px] m-auto flex flex-col gap-4">
        <!-- Graph -->
        {#if currentQuestion.figure}
            <!-- <Graph graph={currentQuestion.graph} {isMath}/> -->
            <img class="m-auto" style:width={isMath ? "400px" : "90%"} src={currentQuestion.figure} alt="Figure">
        {/if}

        <!-- Student's Notes Question | Fiction Question -->
        {#if currentQuestion.intro}
            <div class="intro">{@html currentQuestion.intro}</div>

            <!-- Student's Notes Question-->
            {#if Array.isArray(currentQuestion.passage)}
                <ul class="ml-10 list-disc">
                    {#each currentQuestion.passage as point}
                        <li>{@html point}</li>            
                    {/each}
                </ul>

            <!-- Fiction Question -->
            {:else}
                <p class="ml-8">{@html currentQuestion.passage}</p>
            {/if}

        <!-- Paired Passage Question -->    
        {:else if currentQuestion.passage2}
            <div class="font-bold text-black">Text 1</div>
            <p>{@html currentQuestion.passage}</p>
            <div class="font-bold text-black mt-6">Text 2</div>
            <p>{@html currentQuestion.passage2}</p>

        <!-- Single Passage Question -->
        {:else}
            <p>{@html currentQuestion.passage}</p>
        {/if}
    </div>
{/snippet}

{#snippet question()}
    <div class="question-container max-w-[780px] flex flex-col gap-4 m-auto">
        <!-- Index, Mark, and Elimination Bar -->
        <UI.MarkBar currentQuestionState={currentQuestionAnswer} {isSPR} isWalkthrough />

        <!-- Question -->
        <div class="question-text ml-2">
            {@html currentQuestion.question}
        </div>

        {#if isSPR}
            <UI.SPRAnswerBox {currentQuestion} currentQuestionState={currentQuestionAnswer} isWalkthrough />
        {:else}
            <UI.MultipleChoices {currentQuestion} currentQuestionState={currentQuestionAnswer} isWalkthrough />
        {/if}
    </div>
{/snippet}

<style>
    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>
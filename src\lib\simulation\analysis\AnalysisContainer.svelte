<script lang="ts">
	import { H3, P1, P2 } from "$lib/ui";
	import CircularProgressBar from "./CircularProgressBar.svelte";
    import EstimatedScore from "./EstimatedScore.svelte";
	import SkillAssessmentChart from "./SkillAssessmentChart.svelte";

    const { studentData, skillScores, studentLastAttempt } = $props();

    const tabColors = {
        "Overview": "var(--light-aquamarine)", "Skills Assessment": "var(--light-tangerine)",
        // "Summary": "var(--light-sky-blue)"
    }

    let currentTab = $state("Overview");

    let currentAssessmentTab = $state("Reading");

    const WEAKNESS_REVIEWS = {
        "Student Notes": "These questions are dreadfully long, requiring you to comprehend and select from a lot of information, which might easily lead to fatigue.",
        "Transitions": "The relationship between sentences or arguments can be unclear or confusing, especially when there are 2 or more similar answer choices.",
        "Punctuations": "In academic written English, you need to pay more attention to punctuations rules compared to conversational English. If you do not have a systematic method to learn them, they can overwhelmingly you easily.",
        "Grammar": "Academic written English tends to have stricter rules than conversational English. In addition, you might not be used to English grammar if it is not your mother tongue.",
        "Word in Context": "You found these questions challenging probably because you are more used to popular English contents than academic and scientific pieces. As a result, you lack the ability to guess the meaning of an unfamiliar word based on the words surrounding it.",
        "Reading Comprehension": "Although they do not require any critical thinking, the passages in these questions are usually long and the information is spread throughout sentences, making it harder to scan for the correct answer.",
        "Critical Reading": "These questions are intimidating as the passages are long and full of noise information, which frustrates your ability in making logical inferences to come up with the right answer.",
        "Synthesis Reading": "These are characterized by two sources of information instead of one. Reading, understanding and processing all the information can be really time consuming, especially if you do not yet have a systematic method to deal with these.",
        "Algebra": "Although most of algebra questions are quite fundamental, there are still some that test uncommon formulas. If you are not used to them, it is quite difficult to come up with them on the spot of the exam. On the other hand, easier questions tend to have traps, causing you to lose your precious points.",
        "Data Analysis": "These questions are often long and contain English terminologies that you might not be familiar with. Besides, there are word problems with a lot of details, demanding you to be able to select only the necessary information to save time.",
        "Geometry": "In Geometry, there are a lot of 2D and 3D shapes, each shape has a lot of properties. They can appear burdensome as there are too many formulas and their application varies with each question."
    };

    const STRENGTH_REVIEWS = {
        "Student Notes": "You quickly locate key information by focusing on relevant keywords.",
        "Transitions": "You understand how each argument relates and know how to use transitions to clarify those links.",
        "Punctuations": "You demonstrate clear understanding of standard English conventions.",
        "Grammar": "You show familiarity with the English language and have a solid understanding of English grammar rules.",
        "Word in Context": "You can infer word meanings from surrounding sentences effortlessly.",
        "Reading Comprehension": "You are good at scanning for essential information in the question.",
        "Critical Reading": "You excel at analyzing and interpreting complex texts to make logical inferences.",
        "Synthesis Reading": "You keep track of key points from two sources of information with ease.",
        "Algebra": "You are quick with numbers and calculations.",
        "Data Analysis": "You are adept at keeping track of important information to derive the answer from there.",
        "Geometry": "You have skills in visualizing and dealing with 2D and 3D shapes."
    };
</script>

<div class="container w-[90%] h-[90%] my-auto flex max-w-[1100px]">
    <div class="nav-container h-full flex flex-col gap-2">
        {#each Object.keys(tabColors) as item}
            <button class="nav-item border border-black border-solid py-2 px-5 relative rounded-l-full translate-x-[1px] overflow-hidden" style:background-color={tabColors[item]} onclick={() => currentTab = item} class:tab-active={currentTab === item} style:border-right={currentTab === item ?  `1px solid ${tabColors[item]}` : "1px solid black"}>
                <P1 isBold>{item}</P1>
                <div class="button-overlay w-full h-full bg-black opacity-10 absolute top-0 left-0 hover:opacity-0" class:hidden={currentTab === item}></div>
            </button>
        {/each}
    </div>
    <div class="content-container flex-1 h-full border border-black p-6 rounded-lg rounded-tl-none overflow-auto" style:background-color={tabColors[currentTab]}>
        {@render content?.()}
    </div>
</div>


{#snippet content()}
{#if currentTab === "Overview"}
    <div class="flex gap-4 h-full w-full min-w-fit font-['Inter']">
        <EstimatedScore {studentData} {studentLastAttempt}/>
        <div class="module-scores-card flex-1 border border-black rounded-lg p-4 bg-[var(--light-tangerine)] flex flex-col items-center justify-center gap-6">
            <p class="text-2xl font-semibold">Reading & Writing</p>
            <div class="flex gap-5">
                <CircularProgressBar score={studentData.scores["RW1"]} total={27} text="Module 1" />
                <CircularProgressBar score={studentData.scores["RW2"]} total={27} text="Module 2" />
            </div>
            <p class="text-2xl font-semibold">Math</p>
            <div class="flex gap-5">
                <CircularProgressBar score={studentData.scores["M1"]} total={22} primaryColor="var(--rose)" secondaryColor="var(--light-rose)" text="Module 1" />
                <CircularProgressBar score={studentData.scores["M2"]} total={22} primaryColor="var(--rose)" secondaryColor="var(--light-rose)" text="Module 2" />
            </div>
        </div>
    </div>
    
{:else if currentTab === "Skills Assessment"}
<div class="flex flex-col h-full gap-10">
    <div class="button-container font-['Inter'] font-semibold text-xl flex gap-4">
        {#each ["Reading", "Writing", "Math"] as item}
        <button class="skill-nav-button border border-black border-solid rounded-lg px-6 py-3 bg-[var(--light-sky-blue)]" onclick={() => currentAssessmentTab = item} style:background-color={currentAssessmentTab === item ? "var(--sky-blue)" : "var(--light-sky-blue)"}>{item}</button>
        {/each}
    </div>

    {#key currentAssessmentTab}
    <SkillAssessmentChart specificSkillScores={skillScores[currentAssessmentTab]}/>
    {/key}
</div>
{/if}
<!-- {:else if currentTab === "Summary"}
<div class="flex flex-col gap-6 px-2 pb-4">
    <P2>To know oneself is true progress. From the above insights, now you can have a better idea of your strengths and weaknesses.</P2>
    <H3>Your Strengths</H3>
    <div class="strength-container grid grid-cols-2 gap-4 bg-[var(--light-aquamarine)] border border-black rounded-lg p-4">
        {#each Object.keys(STRENGTH_REVIEWS).slice(0, 4) as skill, index}
        <div class="strength flex gap-4">
            <div class="strength-icon bg-[var(--aquamarine)] w-7 h-7 rounded-full border border-black flex items-center justify-center flex-shrink-0">
                <svg width="18" height="14" viewBox="0 0 18 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3.3158 5.80469L3.31586 5.80462L3.3089 5.79827C3.15075 5.65386 2.96556 5.54165 2.76454 5.46677C2.56355 5.39189 2.34976 5.35545 2.13513 5.35896C1.9205 5.36247 1.70804 5.40589 1.50967 5.48738C1.31127 5.56889 1.13001 5.68727 0.976957 5.83704L0.976956 5.83704C0.823843 5.98687 0.701891 6.16529 0.619523 6.36285C0.537122 6.5605 0.496339 6.77256 0.500258 6.98665C0.504176 7.20072 0.552688 7.41116 0.642145 7.60572C0.73091 7.79877 0.857886 7.97167 1.0145 8.1155L5.38192 12.2438C5.3822 12.2441 5.38248 12.2444 5.38276 12.2446C5.5361 12.3906 5.71692 12.505 5.91415 12.5827C6.11171 12.6605 6.3227 12.7002 6.53519 12.7002C6.74768 12.7002 6.95867 12.6605 7.15623 12.5827C7.35346 12.505 7.53429 12.3905 7.68762 12.2446C7.6879 12.2444 7.68818 12.2441 7.68846 12.2438L16.9703 3.47008C17.1338 3.32554 17.2663 3.14942 17.358 2.95147C17.4513 2.75011 17.5 2.53143 17.5 2.30949C17.5 2.08755 17.4513 1.86888 17.358 1.66752C17.2663 1.4696 17.1339 1.2935 16.9704 1.14897C16.8186 1.00629 16.6404 0.894195 16.4462 0.817734C16.2487 0.739934 16.0377 0.700195 15.8252 0.700195C15.6127 0.700195 15.4017 0.739934 15.2041 0.817734C15.0068 0.895444 14.8259 1.00996 14.6725 1.15599C14.6723 1.15618 14.6721 1.15638 14.6719 1.15657L6.53551 8.85786L3.3158 5.80469Z" fill="#FBDEF0" stroke="black"></path>
                </svg>
            </div>
            <div class="flex flex-col gap-2">
                <p class="text-2xl font-semibold">Strength {index + 1}</p>
                <p class="strength-content text-lg font-[450]" style:font-family="Open Sans">{STRENGTH_REVIEWS[skill]}</p>
            </div>
        </div>
        {/each}
    </div>
    <H3>Your Weaknesses</H3>
    <div class="weakness-container grid grid-cols-2 gap-4 bg-[var(--light-yellow)] border border-black rounded-lg p-6">
        {#each Object.keys(WEAKNESS_REVIEWS).slice(0, 4) as skill, index}
        <div class="weakness flex gap-4">
            <svg class="weakness-icon flex-shrink-0" width="36" height="37" viewBox="0 0 36 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16.4845 3.32519C17.158 2.15853 18.842 2.15853 19.5155 3.3252L31.6399 24.3252C32.3135 25.4919 31.4715 26.9502 30.1244 26.9502H5.87564C4.52849 26.9502 3.68652 25.4919 4.3601 24.3252L16.4845 3.32519Z" fill="#FFC800" stroke="black" stroke-width="0.5"></path><rect x="16.5" y="20.2002" width="3" height="3" rx="1.5" fill="black"></rect><rect x="16.5" y="9.2002" width="3" height="9" rx="1.5" fill="black"></rect>
            </svg>
            <div class="flex flex-col gap-2">
                <p class="text-2xl font-semibold">Weakness {index + 1}</p>
                <p class="weakness-content text-lg font-[450]" style:font-family="Open Sans">{WEAKNESS_REVIEWS[skill]}</p>
            </div>
        </div>
        {/each}
    </div>
</div>
{/if} -->
{/snippet}

<style>
    .tab-active {
        box-shadow: 0 0.25rem 0.25rem var(--pitch-black);
    }

    .content-container {
        box-shadow: 0.25rem 0.25rem 0.25rem var(--pitch-black);
    }

    .module-scores-card, .strength-icon {
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .skill-nav-button {
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        width: 150px;
    }

    .skill-nav-button:active {
        box-shadow: none;
        translate: 0.25rem 0.25rem;
    }


    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        margin: 4px 0;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>
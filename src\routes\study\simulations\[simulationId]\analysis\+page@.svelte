<script lang="ts">
    import { page } from '$app/state';
	import Analysis from '$lib/simulation/Analysis.svelte';
	import { P2 } from '$lib/ui';
	import { onMount } from 'svelte';

    let simulationId = $derived(page.params.simulationId);

    let { data } = $props();

    let isLoadingAnalysis = $state(true);

    let modules = $state(null);
    let studentData = $state(null);

    async function getData() {
        const simulationData = await fetch('/api/simulation/get-simulation-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ simulationId })
        });

        const { data: simulation, success: simulationSuccess, message: simulationMessage } = await simulationData.json();

        if (!simulationSuccess) {
            alert(`Error: ${simulationMessage}`);
            return {};
        }

        const modules = simulation.modules;
        
        const studentDataAPI = await fetch('/api/simulation/get-student-analysis-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ simulationId, modules })
        });

        const { data: studentData, success: studentDataSuccess, message: studentDataMessage } = await studentDataAPI.json();

        if (!studentDataSuccess) {
            alert(`Error: ${studentDataMessage}`);
            return {};
        }

        return { modules, studentData };
    }

    onMount(async () => {
        const { modules: tempModules, studentData: tempStudentData } = await getData();
        modules = tempModules;
        studentData = tempStudentData;
        isLoadingAnalysis = false;
    });
</script>

{#if isLoadingAnalysis}
    <div class="fixed inset-0 loading-container flex items-center justify-center gap-4">
        <div class="loading-spinner w-8 h-8 border-4 border-[var(--light-sky-blue)] border-t-[var(--sky-blue)] rounded-full animate-spin"></div>
        <P2>Loading Analysis...</P2>
    </div>
{:else}
    <Analysis {modules} {studentData} {simulationId}/>
{/if}
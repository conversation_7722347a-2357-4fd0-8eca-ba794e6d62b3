<!-- 
    @component
    ## MinitestIntro
    The introduction to the minitest.
-->

<script>
    import { Check<PERSON>, Slider } from "$lib/ui";
    import { updateDoc, doc } from 'firebase/firestore';
    import { db } from '$lib/firebase';

    let { uid } = $props();

    let noAim = $state(false);
    let noPrevious = $state(false);
    
    let aimScore = $state(1500);
    let verbalScore = $state(500);
    let mathScore = $state(500);
    
    let currentScore = $derived(verbalScore + mathScore);

    export const saveData = async () => {
        // const docRef = doc(db, 'users', uid);

        // // Update the user's aim score and current score
        // await updateDoc(docRef, {
        //     aimScore: !noAim ? aimScore : null,
        //     previousVerbalScore: !noPrevious ? verbalScore : null,
        //     previousMathScore: !noPrevious ? mathScore : null
        // });

        // // Cache the user's aim score and current score
        // localStorage.setItem('aimScore', !noAim ? aimScore : null);
        // localStorage.setItem('previousVerbalScore', !noPrevious ? verbalScore : null);
        // localStorage.setItem('previousMathScore', !noPrevious ? mathScore : null);
    }
</script>

<div class="minitest-intro flex flex-col gap-6 p-6 mt-[30px]">
    <div class="intro-title text-3xl text-center">DSAT16 Simulation</div>
    <div class="intro-text text-lg flex flex-col gap-4">
        <p>Before you start, we would like to ask you some questions regarding your plan and experience with the Digital SAT.</p>
        <p>Your answer will be taken into consideration to deliver a comprehensive analysis of your result and a personalized study plan.</p>    
    </div>
    <div class="intro-questions flex flex-col gap-4">
        <Slider label={"What is your target SAT score?"} min={400} max={1600} step={10} bind:value={aimScore}/>
        <Checkbox label={"I just want to improve my score as much as possible."} bind:isChecked={noAim}/>
    </div>
    <div class="intro-questions flex flex-col gap-4">
        <Slider label={"What is your current SAT score?"} min={400} max={1600} step={10} value={currentScore} disabled={true}/>
        <Slider label={"Verbal"} min={200} max={800} step={10} bind:value={verbalScore} isRightAligned={true}/>
        <Slider label={"Math"} min={200} max={800} step={10} bind:value={mathScore} isRightAligned={true}/>
        <Checkbox label={"I have never taken an SAT test or mock test before."} bind:isChecked={noPrevious}/>
    </div>
</div>